{"name": "Kubeconfig Upload Workflow", "nodes": [{"parameters": {"httpMethod": "POST", "path": "upload-kubeconfig", "options": {}}, "id": "1a5f6bff-7352-4d8e-9bcf-c5c51ea7d42b", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [250, 300]}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.clusterName}}", "operation": "isEmpty"}]}}, "id": "a8a7e0d2-3c1b-4e0f-9f8d-8d4f1d8e9a7e", "name": "IF (Empty Cluster Name)", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [450, 300]}, {"parameters": {"jsCode": "// Get the cluster name from the request\nconst clusterName = $input.item.json.clusterName;\n\n// Get the kubeconfig path from environment variable\nconst kubeconfigPath = process.env.KUBECONFIG_PATH || '/var/www/html/storage/app/kubeconfigs';\n\n// Check if the file exists\nconst fs = require('fs');\nconst path = require('path');\nconst filePath = path.join(kubeconfigPath, clusterName);\n\nlet exists = false;\ntry {\n  exists = fs.existsSync(filePath);\n} catch (error) {\n  // Handle error\n}\n\nreturn {\n  exists,\n  clusterName,\n  filePath\n};"}, "id": "b9b8e1d3-4c2c-5f1f-0f9e-9e5f2e9a8b7e", "name": "Check If Cluster Exists", "type": "n8n-nodes-base.code", "typeVersion": 1, "position": [650, 200]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{$json.exists}}", "value2": true}]}}, "id": "c0c9f2e4-5d3d-6f2f-1f0f-0f6f3f0a9c8f", "name": "IF (Cluster Exists)", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [850, 200]}, {"parameters": {"jsCode": "// Return error message\nreturn {\n  success: false,\n  message: `A cluster with the name '${$input.item.json.clusterName}' already exists.`\n};"}, "id": "d1d0f3e5-6e4e-7f3f-2f1f-1f7f4f1b0d9f", "name": "Error: Cluster Exists", "type": "n8n-nodes-base.code", "typeVersion": 1, "position": [1050, 100]}, {"parameters": {"jsCode": "// Get the binary data from the request\nconst binaryData = $input.item.binary.file;\nconst clusterName = $input.item.json.clusterName;\nconst kubeconfigPath = process.env.KUBECONFIG_PATH || '/var/www/html/storage/app/kubeconfigs';\n\n// Save the file\nconst fs = require('fs');\nconst path = require('path');\nconst filePath = path.join(kubeconfigPath, clusterName);\n\ntry {\n  // Ensure directory exists\n  if (!fs.existsSync(kubeconfigPath)) {\n    fs.mkdirSync(kubeconfigPath, { recursive: true });\n  }\n  \n  // Convert base64 to buffer and save\n  const buffer = Buffer.from(binaryData.data, 'base64');\n  fs.writeFileSync(filePath, buffer);\n  \n  return {\n    success: true,\n    message: `Cluster '${clusterName}' has been successfully uploaded.`,\n    filePath\n  };\n} catch (error) {\n  return {\n    success: false,\n    message: `Failed to save cluster: ${error.message}`,\n    error: error.toString()\n  };\n}"}, "id": "e2e1f4f6-7f5f-8f4f-3f2f-2f8f5f2c1e0f", "name": "Save Kubeconfig File", "type": "n8n-nodes-base.code", "typeVersion": 1, "position": [1050, 300]}, {"parameters": {"jsCode": "// Return error message for missing cluster name\nreturn {\n  success: false,\n  message: \"Cluster name is required.\"\n};"}, "id": "f3f2f5f7-8f6f-9f5f-4f3f-3f9f6f3d2f1f", "name": "Error: Missing Cluster Name", "type": "n8n-nodes-base.code", "typeVersion": 1, "position": [650, 400]}, {"parameters": {"respondWith": "json", "responseBody": "={{$json}}", "options": {}}, "id": "g4g3g6g8-9g7g-0g6g-5g4g-4g0g7g4e3g2g", "name": "Respond To Webhook", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1250, 200]}], "connections": {"Webhook": {"main": [[{"node": "IF (Empty Cluster Name)", "type": "main", "index": 0}]]}, "IF (Empty Cluster Name)": {"main": [[{"node": "Error: Missing Cluster Name", "type": "main", "index": 0}], [{"node": "Check If Cluster Exists", "type": "main", "index": 0}]]}, "Check If Cluster Exists": {"main": [[{"node": "IF (Cluster Exists)", "type": "main", "index": 0}]]}, "IF (Cluster Exists)": {"main": [[{"node": "Error: Cluster Exists", "type": "main", "index": 0}], [{"node": "Save Kubeconfig File", "type": "main", "index": 0}]]}, "Error: Cluster Exists": {"main": [[{"node": "Respond To Webhook", "type": "main", "index": 0}]]}, "Save Kubeconfig File": {"main": [[{"node": "Respond To Webhook", "type": "main", "index": 0}]]}, "Error: Missing Cluster Name": {"main": [[{"node": "Respond To Webhook", "type": "main", "index": 0}]]}}}