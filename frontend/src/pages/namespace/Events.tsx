import React, { useState, useMemo } from 'react';
import DataTable from '../../components/DataTable';
import { GridColDef } from '@mui/x-data-grid';
import { useEvents } from '../../hooks/namespace/useEvents';
import { Typography, Box, Link } from '@mui/material';
import { formatTimeAgo } from '../../utils/formDate';
import { useNamespace } from '../../contexts/NamespaceContext';
import NamespaceFilter from '../../components/NamespaceFilter';
import SearchInput from '../../components/SearchInput';

const Events: React.FC = () => {
  const { events, loading, error } = useEvents();
  const { selectedNamespaces } = useNamespace();
  const [searchTerm, setSearchTerm] = useState('');

  const columns: GridColDef[] = [
    { field: 'type', headerName: 'Type', width: 100 },
    { field: 'message', headerName: 'Message', width: 300 },
    { field: 'namespace', headerName: 'Namespace', width: 120 },
    {
      field: 'involvedObject',
      headerName: 'Involved Object',
      width: 200,
      renderCell: (params) => {
        const involvedObject = params.value;
        return (
          <Link
            href="#"
            onClick={(e) => {
              e.preventDefault();
              // You could add navigation to the involved object here
            }}
            sx={{ color: '#1976d2', textDecoration: 'underline' }}
          >
            {`${involvedObject.kind}: ${involvedObject.name}`}
          </Link>
        );
      }
    },
    { field: 'source', headerName: 'Source', width: 150 },
    { field: 'count', headerName: 'Count', width: 80, type: 'number' },
    { field: 'age', headerName: 'Age', width: 100 },
    { field: 'lastSeen', headerName: 'Last Seen', width: 100 },
  ];

  const filteredEvents = useMemo(() => {
    if (!events) return [];

    let result = events;

    // Filter by selected namespaces
    if (selectedNamespaces && selectedNamespaces.length > 0 && !selectedNamespaces.includes('all')) {
      result = result.filter(event => selectedNamespaces.includes(event.metadata.namespace));
    }

    // Filter by search term
    if (searchTerm.trim() !== '') {
      const lowerSearch = searchTerm.toLowerCase();
      result = result.filter(event => {
        const searchableFields = {
          type: event.type,
          message: event.message,
          namespace: event.metadata.namespace,
          involvedObject: `${event.involvedObject.kind}: ${event.involvedObject.name}`,
          source: event.source.component,
          count: event.count.toString(),
          age: formatTimeAgo(event.firstTimestamp),
          lastSeen: formatTimeAgo(event.lastTimestamp),
        };

        return Object.values(searchableFields).some(value => {
          if (typeof value === 'string' || typeof value === 'number') {
            return value.toString().toLowerCase().includes(lowerSearch);
          }
          return false;
        });
      });
    }

    return result;
  }, [events, selectedNamespaces, searchTerm]);

  const rows = (filteredEvents ?? []).map((event, index) => {
    return {
      id: index + 1,
      type: event.type,
      message: event.message,
      namespace: event.metadata.namespace,
      involvedObject: event.involvedObject,
      source: event.source.component + (event.source.host ? ` (${event.source.host})` : ''),
      count: event.count,
      age: formatTimeAgo(event.firstTimestamp),
      lastSeen: formatTimeAgo(event.lastTimestamp),
    };
  });

  return (
    <>
      <Box className="p-2">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center mb-4">
          <NamespaceFilter />
          <SearchInput value={searchTerm} onChange={setSearchTerm} />
        </div>
        {error ? (
          <Typography color="error">{error}</Typography>
        ) : (
          <DataTable
            rows={rows}
            columns={columns}
            loading={loading}
          />
        )}
      </Box>
    </>
  );
};

export default Events;
