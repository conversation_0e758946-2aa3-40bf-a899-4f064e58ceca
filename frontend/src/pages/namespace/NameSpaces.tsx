import React, {useState, useMemo} from 'react';
import DataTable from '../../components/DataTable';
import { GridColDef } from '@mui/x-data-grid';
import { useFilterNameSpace } from '../../hooks/filter/useFilterNameSpaces';
import { Typography,Box } from '@mui/material';
import { formatAgeInDays } from '../../utils/formDate';
import SearchInput from '../../components/SearchInput';

const NameSpaces: React.FC = () => {
  const { namespace, loading, error } = useFilterNameSpace();
  const [searchTerm, setSearchTerm] = useState('');
 
  const columns: GridColDef[] = [
    { field: 'id', headerName: 'ID', width: 80 },
    { field: 'name', headerName: 'Name', width: 300 },
    { field: 'labels', headerName: 'Labels', width: 350 },
    { field: 'age', headerName: 'Age', width: 100 },
  ];
  

  const filteredNameSpace = useMemo(() => {
              if (!namespace) return [];
            
              let result = namespace;
            
              if (searchTerm.trim() !== '') {
                const lowerSearch = searchTerm.toLowerCase();
                result = result.filter(name => {
                    const namespace = {
                        name: name.metadata.name,
                        age: formatAgeInDays(name.metadata.creationTimestamp),
                      };
                      
            
                  return Object.values(namespace).some(value => {
                    if (typeof value === 'string' || typeof value === 'number') {
                      return value.toString().toLowerCase().includes(lowerSearch);
                    }
                    return false;
                  });
                });
              }
            
              return result;
            }, [namespace, searchTerm]);
            const rows = (filteredNameSpace ?? []).map((name, index) => {
                return {
                  id: index + 1,
                  name: name.metadata.name,
                  labels: name.metadata.labels ? Object.entries(name.metadata.labels).map(([key, value]) => `${key}: ${value}`).join(', ') : 'No labels',
                  age: formatAgeInDays(name.metadata.creationTimestamp),
                };
              });
              
  return (
    <>
    <Box className="p-2">
  <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center mb-4">
    <SearchInput value={searchTerm} onChange={setSearchTerm} />
  </div>
      {error ? (
        <Typography color="error">{error}</Typography>
      ) : (
        <DataTable
          rows={rows}
          columns={columns}
          loading={loading}
        />
      )}
      </Box>
    </>
  );
};

export default NameSpaces;