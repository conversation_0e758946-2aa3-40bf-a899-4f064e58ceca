import React, {useState, useMemo} from 'react';
import DataTable from '../../components/DataTable';
import { GridColDef } from '@mui/x-data-grid';
import { Typography,Box } from '@mui/material';
import SearchInput from '../../components/SearchInput';
import { useIngresseClasses } from '../../hooks/network/useIngressClasses';

const IngresseClasses: React.FC = () => {
  const { ingresseClass, loading, error } = useIngresseClasses();
  const [searchTerm, setSearchTerm] = useState('');
 
  const columns: GridColDef[] = [
    { field: 'id', headerName: 'ID', width: 70 },
    { field: 'name', headerName: 'Name', width: 300 },
    { field: 'namespace', headerName: 'Namespace', width: 200 },
    { field: 'controller', headerName: 'Controller', width: 180 },
    { field: 'apiGroup', headerName: 'API Group', width: 200 },
    { field: 'scope', headerName: 'Scope', width: 150 },
    { field: 'kind', headerName: 'Kind', width: 150 },
  ];
  

  const filteredIngresseClass = useMemo(() => {
              if (!ingresseClass) return [];
            
              let result = ingresseClass;
            
              if (searchTerm.trim() !== '') {
                const lowerSearch = searchTerm.toLowerCase();
                result = result.filter(ing => {
                  const ingresseClass = {
                    name: ing.metadata.name,
                    namespace: ing.metadata.namespace,
                    controller: ing.spec?.controller || '',
                    apiGroup: ing.apiVersion?.split('/')[0] || '',
                    scope: ing.scope || '',
                    kind: ing.kind || '',
                  };
            
                  return Object.values(ingresseClass).some(value => {
                    if (typeof value === 'string' || typeof value === 'number') {
                      return value.toString().toLowerCase().includes(lowerSearch);
                    }
                    return false;
                  });
                });
              }
            
              return result;
            }, [ingresseClass, searchTerm]);
            const rows = (filteredIngresseClass ?? []).map((ing, index) => {
                return {
                  id: index + 1,
                  name: ing.metadata.name,
                  namespace: ing.metadata.namespace || '',
                  controller: ing.spec?.controller || '—',
                  apiGroup: ing.apiVersion?.split('/')[0] || '—',
                  scope: ing.scope || '—',
                  kind: ing.kind || '—',
                };
              });
              
  return (
    <>
    <Box className="p-2">
  <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center mb-4">
    <SearchInput value={searchTerm} onChange={setSearchTerm} />
  </div>
      {error ? (
        <Typography color="error">{error}</Typography>
      ) : (
        <DataTable
          rows={rows}
          columns={columns}
          loading={loading}
        />
      )}
      </Box>
    </>
  );
};

export default IngresseClasses;