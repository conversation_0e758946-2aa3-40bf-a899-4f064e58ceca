import React, {useState, useMemo} from 'react';
import DataTable from '../../components/DataTable';
import { GridColDef } from '@mui/x-data-grid';
import { useServices } from '../../hooks/network/useServices';
import { Typography,Box } from '@mui/material';
import { formatAgeInDays } from '../../utils/formDate';
import { useNamespace } from '../../contexts/NamespaceContext';
import NamespaceFilter from '../../components/NamespaceFilter';
import SearchInput from '../../components/SearchInput';

const Services: React.FC = () => {
  const { services, loading, error } = useServices();
  const { selectedNamespaces} = useNamespace();
  const [searchTerm, setSearchTerm] = useState('');
 
  const columns: GridColDef[] = [
    { field: 'id', headerName: 'ID', width: 80 },
    { field: 'name', headerName: 'Name', width: 200 },
    { field: 'namespace', headerName: 'Namespace', width: 150 },
    { field: 'type', headerName: 'Type', width: 120 },
    { field: 'clusterIP', headerName: 'Cluster IP', width: 150 },
    { field: 'ports', headerName: 'Ports', width: 200 },
    { field: 'externalIP', headerName: 'External IP', width: 200 },
    { field: 'selector', headerName: 'Selector', width: 200 },
    { field: 'age', headerName: 'Age', width: 120 },
  ];
  

  const filteredServices = useMemo(() => {
              if (!services) return [];
            
              let result = selectedNamespaces.includes('all')
                ? services
                : services.filter(service => selectedNamespaces.includes(service.metadata.namespace));
            
              if (searchTerm.trim() !== '') {
                const lowerSearch = searchTerm.toLowerCase();
                result = result.filter(service => {
                  const services = {
                    name: service.metadata.name,
                    namespace: service.metadata.namespace,
                    type: service.spec.type || 'ClusterIP',
                    clusterIP: service.spec.clusterIP || 'None',
                    ports: service.spec.ports.map(p => `${p.port}${p.nodePort ? `:${p.nodePort}` : ''}/${p.protocol || 'TCP'}`).join(', '),
                    externalIP: service.spec.externalIPs?.join(', ') ||
                      service.status?.loadBalancer?.ingress?.map(i => i.ip || i.hostname).join(', ') || 'None',
                    selector: service.spec.selector
                      ? Object.entries(service.spec.selector).map(([key, value]) => `${key}=${value}`).join(', ')
                      : 'None',
                    age: formatAgeInDays(service.metadata.creationTimestamp),
                  };
            
                  return Object.values(services).some(value => {
                    if (typeof value === 'string' || typeof value === 'number') {
                      return value.toString().toLowerCase().includes(lowerSearch);
                    }
                    return false;
                  });
                });
              }
            
              return result;
            }, [services, selectedNamespaces, searchTerm]);
            const rows = (filteredServices ?? []).map((service, index) => {
              const externalIPs = service.spec.externalIPs?.join(', ') ||
                service.status?.loadBalancer?.ingress?.map(i => i.ip || i.hostname).join(', ') || 'None';
            
              const selector = service.spec.selector
                ? Object.entries(service.spec.selector).map(([key, value]) => `${key}=${value}`).join(', ')
                : 'None';
            
              const ports = service.spec.ports.map(p => `${p.port}${p.nodePort ? `:${p.nodePort}` : ''}/${p.protocol || 'TCP'}`).join(', ');
            
              return {
                id: index + 1,
                name: service.metadata.name,
                namespace: service.metadata.namespace || 'default',
                type: service.spec.type || 'ClusterIP',
                clusterIP: service.spec.clusterIP || 'None',
                ports,
                externalIP: externalIPs,
                selector,
                age: formatAgeInDays(service.metadata.creationTimestamp),
              };
            });
            
  return (
    <>
    <Box className="p-2">
  <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center mb-4">
    <NamespaceFilter />
    <SearchInput value={searchTerm} onChange={setSearchTerm} />
  </div>
      {error ? (
        <Typography color="error">{error}</Typography>
      ) : (
        <DataTable
          rows={rows}
          columns={columns}
          loading={loading}
        />
      )}
      </Box>
    </>
  );
};

export default Services;