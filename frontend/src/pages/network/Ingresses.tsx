import React, { useState, useMemo } from 'react';
import DataTable from '../../components/DataTable';
import { GridColDef } from '@mui/x-data-grid';
import { useIngresses } from '../../hooks/network/useIngresses';
import { Typography, Box } from '@mui/material';
import { formatAgeInDays } from '../../utils/formDate';
import { useNamespace } from '../../contexts/NamespaceContext';
import NamespaceFilter from '../../components/NamespaceFilter';
import SearchInput from '../../components/SearchInput';

const Ingresses: React.FC = () => {
  const { ingresses, loading, error } = useIngresses();
  const { selectedNamespaces } = useNamespace();
  const [searchTerm, setSearchTerm] = useState('');

  const columns: GridColDef[] = [
    { field: 'id', headerName: 'ID', width: 100 },
    { field: 'name', headerName: 'Name', width: 500 },
    { field: 'namespace', headerName: 'Namespace', width: 200 },
    { field: 'loadBalancer', headerName: 'LoadBalancer', width: 200 },
    { field: 'rules', headerName: 'Rules', width: 200,},
    
  { field: 'age', headerName: 'Age', width: 120 },
  ];
  

  const filteredIngresses = useMemo(() => {
    if (!ingresses) return [];

    let result = selectedNamespaces.includes('all')
      ? ingresses
      : ingresses.filter(ing => selectedNamespaces.includes(ing.metadata.namespace));

    if (searchTerm.trim() !== '') {
      const lowerSearch = searchTerm.toLowerCase();
      result = result.filter(ing => {
        const Ingresses = {
          name: ing.metadata.name,
          namespace: ing.metadata.namespace,
          age: formatAgeInDays(ing.metadata.creationTimestamp),
          loadBalancer: ing.status?.loadBalancer?.ingress
            ? ing.status.loadBalancer.ingress.map(lb => lb.ip || lb.hostname).join(', ')
            : 'N/A',
          rules: ing.spec?.rules
            ? ing.spec.rules.map(rule => `https://${rule.host}`).join(', ')
            : 'N/A',
        };

        return Object.values(Ingresses).some(value => {
          if (typeof value === 'string' || typeof value === 'number') {
            return value.toString().toLowerCase().includes(lowerSearch);
          }
          return false;
        });
      });
    }

    return result;
  }, [ingresses, selectedNamespaces, searchTerm]);

  const rows = (filteredIngresses ?? []).map((ing, index) => {
    return {
      id: index + 1,
      name: ing.metadata.name,
      namespace: ing.metadata.namespace || 'default',
      age: formatAgeInDays(ing.metadata.creationTimestamp),
      loadBalancer: ing.status?.loadBalancer?.ingress
        ? ing.status.loadBalancer.ingress.map(lb => lb.ip || lb.hostname).join(', ')
        : 'N/A',
      rules: ing.spec?.rules
        ? ing.spec.rules.map(rule => `https://${rule.host}`).join(', ')
        : 'N/A',
    };
  });

  return (
    <>
      <Box className="p-2">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center mb-4">
          <NamespaceFilter />
          <SearchInput value={searchTerm} onChange={setSearchTerm} />
        </div>
        {error ? (
          <Typography color="error">{error}</Typography>
        ) : (
          <DataTable
            rows={rows}
            columns={columns}
            loading={loading}
          />
        )}
      </Box>
    </>
  );
};

export default Ingresses;
