import React, {useState, useMemo} from 'react';
import DataTable from '../../components/DataTable';
import { GridColDef } from '@mui/x-data-grid';
import { useNetworkPolicies } from '../../hooks/network/useNetworkPolicies';
import { Typography,Box } from '@mui/material';
import { formatAgeInDays } from '../../utils/formDate';
import { useNamespace } from '../../contexts/NamespaceContext';
import NamespaceFilter from '../../components/NamespaceFilter';
import SearchInput from '../../components/SearchInput';

const NetworkPolicies: React.FC = () => {
  const { networkPolicies, loading, error } = useNetworkPolicies();
  const { selectedNamespaces} = useNamespace();
  const [searchTerm, setSearchTerm] = useState('');
 
  const columns: GridColDef[] = [
    { field: 'id', headerName: 'ID', width: 100 },
    { field: 'name', headerName: 'Name', width: 500 },
    { field: 'namespace', headerName: 'Namespace', width: 200 },
    { field: 'policyTypes', headerName: 'Policy Types', width: 200 },
    { field: 'age', headerName: 'Age', width: 120 },
  ];

  const filteredNetworkPolicy = useMemo(() => {
              if (!networkPolicies) return [];
            
              let result = selectedNamespaces.includes('all')
                ? networkPolicies
                : networkPolicies.filter(config => selectedNamespaces.includes(config.metadata.namespace));
            
              if (searchTerm.trim() !== '') {
                const lowerSearch = searchTerm.toLowerCase();
                result = result.filter(net=> {
                  const networkPolicies = {
                    name: net.metadata.name,
                    namespace: net.metadata.namespace,
                    policyNetwork: net.spec?.policyTypes
                      ? net.spec.policyTypes.map(type => type).join(', ')
                      : 'N/A',
                    age: formatAgeInDays(net.metadata.creationTimestamp),
                  };
            
                  return Object.values(networkPolicies).some(value => {
                    if (typeof value === 'string' || typeof value === 'number') {
                      return value.toString().toLowerCase().includes(lowerSearch);
                    }
                    return false;
                  });
                });
              }
            
              return result;
            }, [networkPolicies, selectedNamespaces, searchTerm]);
  const rows = (filteredNetworkPolicy ?? []).map((net, index) => {
    const policyTypes = net.spec?.policyTypes
      ? net.spec.policyTypes.map(type => type).join(', ')
      : 'N/A';
    return {
      id: index + 1,
      name: net.metadata.name,
      namespace: net.metadata.namespace || 'default',
      policyTypes: policyTypes,
      age: formatAgeInDays(net.metadata.creationTimestamp),
    };
  });
  return (
    <>
    <Box className="p-2">
  <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center mb-4">
    <NamespaceFilter />
    <SearchInput value={searchTerm} onChange={setSearchTerm} />
  </div>
      {error ? (
        <Typography color="error">{error}</Typography>
      ) : (
        <DataTable
          rows={rows}
          columns={columns}
          loading={loading}
        />
      )}
      </Box>
    </>
  );
};

export default NetworkPolicies;