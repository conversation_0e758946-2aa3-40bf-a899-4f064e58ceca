import React, {useState, useMemo} from 'react';
import DataTable from '../../components/DataTable';
import { GridColDef } from '@mui/x-data-grid';
import {useEndpoints} from '../../hooks/network/useEndpoints';
import { Typography,Box } from '@mui/material';
import { formatAgeInDays } from '../../utils/formDate';
import { useNamespace } from '../../contexts/NamespaceContext';
import NamespaceFilter from '../../components/NamespaceFilter';
import SearchInput from '../../components/SearchInput';

const Endpoints: React.FC = () => {
  const { endpoints, loading, error } = useEndpoints();
  const { selectedNamespaces} = useNamespace();
  const [searchTerm, setSearchTerm] = useState('');
 
  const columns: GridColDef[] = [
    { field: 'id', headerName: 'ID', width: 100 },
    { field: 'name', headerName: 'Name', width: 500 },
    { field: 'namespace', headerName: 'Namespace', width: 200 },
    { field: 'addresses', headerName: 'Endpoints', width: 400 },
    { field: 'age', headerName: 'Age', width: 120 },
  ];

  const filteredEndpoint = useMemo(() => {
              if (!endpoints) return [];
            
              let result = selectedNamespaces.includes('all')
                ? endpoints
                : endpoints.filter(endpoint => selectedNamespaces.includes(endpoint.metadata.namespace));
            
              if (searchTerm.trim() !== '') {
                const lowerSearch = searchTerm.toLowerCase();
                result = result.filter(endpoint => {
                  const Endpoint = {
                    name: endpoint.metadata.name,
                    namespace: endpoint.metadata.namespace,
                    addresses: endpoint.subsets?.flatMap(subset => {
                      const ips = subset.addresses ?? [];
                      const ports = subset.ports ?? [];
              
                      return ips.flatMap(ip =>
                        ports.map(port => `${ip.ip}:${port.port}`)
                      );
                    })?.join(', ') || 'None',
                    age: formatAgeInDays(endpoint.metadata.creationTimestamp),
                  };
            
                  return Object.values(Endpoint).some(value => {
                    if (typeof value === 'string' || typeof value === 'number') {
                      return value.toString().toLowerCase().includes(lowerSearch);
                    }
                    return false;
                  });
                });
              }
            
              return result;
            }, [endpoints, selectedNamespaces, searchTerm]);
            const rows = (filteredEndpoint ?? []).map((endpoint, index) => {
                const addresses = endpoint.subsets?.flatMap(subset => {
                  const ips = subset.addresses ?? [];
                  const ports = subset.ports ?? [];
              
                  return ips.flatMap(ip =>
                    ports.map(port => `${ip.ip}:${port.port}`)
                  );
                }) ?? [];
              
                return {
                  id: index + 1,
                  name: endpoint.metadata.name,
                  namespace: endpoint.metadata.namespace || 'default',
                  age: formatAgeInDays(endpoint.metadata.creationTimestamp),
                  addresses: addresses.join(', ') || 'None',
                };
              });
              
  return (
    <>
    <Box className="p-2">
  <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center mb-4">
    <NamespaceFilter />
    <SearchInput value={searchTerm} onChange={setSearchTerm} />
  </div>
      {error ? (
        <Typography color="error">{error}</Typography>
      ) : (
        <DataTable
          rows={rows}
          columns={columns}
          loading={loading}
        />
      )}
      </Box>
    </>
  );
};

export default Endpoints;