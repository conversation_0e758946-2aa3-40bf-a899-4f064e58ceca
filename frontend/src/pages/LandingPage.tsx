import React from 'react';
import { Link } from 'react-router-dom';

const LandingPage: React.FC = () => {
  return (
    <div className="min-h-screen flex flex-col bg-gray-100">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 flex justify-between items-center">
          <h1 className="text-2xl font-bold text-red-600">RedAlerts</h1>
          <Link 
            to="/login" 
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition duration-200"
          >
            Login
          </Link>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-grow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          {/* Hero Section */}
          <div className="text-center mb-16">
            <h2 className="text-4xl font-extrabold text-gray-900 sm:text-5xl sm:tracking-tight lg:text-6xl">
              Welcome to RedAlerts
            </h2>
            <p className="mt-5 max-w-xl mx-auto text-xl text-gray-500">
              A comprehensive Kubernetes monitoring and management platform
            </p>
          </div>

          {/* Features Section */}
          <div className="mt-12">
            <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
              {/* Feature 1 */}
              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                  <h3 className="text-lg font-medium text-gray-900">Real-time Monitoring</h3>
                  <p className="mt-2 text-sm text-gray-500">
                    Monitor your Kubernetes clusters in real-time with comprehensive dashboards and alerts.
                  </p>
                </div>
              </div>

              {/* Feature 2 */}
              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                  <h3 className="text-lg font-medium text-gray-900">Resource Management</h3>
                  <p className="mt-2 text-sm text-gray-500">
                    Efficiently manage your Kubernetes resources including nodes, pods, services, and more.
                  </p>
                </div>
              </div>

              {/* Feature 3 */}
              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                  <h3 className="text-lg font-medium text-gray-900">Security Insights</h3>
                  <p className="mt-2 text-sm text-gray-500">
                    Get insights into your cluster's security posture and identify potential vulnerabilities.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <p className="text-center text-gray-500 text-sm">
            &copy; {new Date().getFullYear()} RedAlerts. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  );
};

export default LandingPage;
