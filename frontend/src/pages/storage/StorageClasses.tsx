import React, {useState, useMemo} from 'react';
import DataTable from '../../components/DataTable';
import { GridColDef } from '@mui/x-data-grid';
import { useStorageClasses } from '../../hooks/storage/useStorageClasses';
import { Typography,Box } from '@mui/material';
import { formatAgeInDays } from '../../utils/formDate';
import SearchInput from '../../components/SearchInput';

const StorageClasses: React.FC = () => {
  const { storage, loading, error } = useStorageClasses();
  const [searchTerm, setSearchTerm] = useState('');
 
  const columns: GridColDef[] = [
    { field: 'id', headerName: 'ID', width: 80 },
    { field: 'name', headerName: 'Name', width: 250 },
    { field: 'provisioner', headerName: 'Provisioner', width: 250 },
    { field: 'reclaimPolicy', headerName: 'Reclaim Policy', width: 180 },
    { field: 'default', headerName: 'Default', width: 120 },
    { field: 'age', headerName: 'Age', width: 100 },
  ];
  

  const filteredStorage = useMemo(() => {
              if (!storage) return [];
            
              let result = storage;
            
              if (searchTerm.trim() !== '') {
                const lowerSearch = searchTerm.toLowerCase();
                result = result.filter(store => {
                    const annotations = (store.metadata as { annotations?: Record<string, string> })?.annotations || {};
                    const storage = {
                        name: store.metadata.name,
                        provisioner: store.provisioner,
                        reclaimPolicy: store.reclaimPolicy || '',
                        default: annotations['storageclass.kubernetes.io/is-default-class'] === 'true' ? 'yes' : 'no',
                        age: formatAgeInDays(store.metadata.creationTimestamp),
                      };
                      
                      
            
                  return Object.values(storage).some(value => {
                    if (typeof value === 'string' || typeof value === 'number') {
                      return value.toString().toLowerCase().includes(lowerSearch);
                    }
                    return false;
                  });
                });
              }
            
              return result;
            }, [storage, searchTerm]);
            const rows = (filteredStorage ?? []).map((store, index) => {
                const annotations = (store.metadata as { annotations?: Record<string, string> })?.annotations || {};
                const isDefault = annotations['storageclass.kubernetes.io/is-default-class'] === 'true';
              
                return {
                  id: index + 1,
                  name: store.metadata.name,
                  provisioner: store.provisioner,
                  reclaimPolicy: store.reclaimPolicy || '—',
                  default: isDefault ? 'Yes' : 'No',
                  age: formatAgeInDays(store.metadata.creationTimestamp),
                };
              });
              
            
              
  return (
    <>
    <Box className="p-2">
  <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center mb-4">
    <SearchInput value={searchTerm} onChange={setSearchTerm} />
  </div>
      {error ? (
        <Typography color="error">{error}</Typography>
      ) : (
        <DataTable
          rows={rows}
          columns={columns}
          loading={loading}
        />
      )}
      </Box>
    </>
  );
};

export default StorageClasses