import React, {useState, useMemo} from 'react';
import DataTable from '../../components/DataTable';
import { GridColDef } from '@mui/x-data-grid';
import { usePersistantVolume } from '../../hooks/storage/usePersistantVolume';
import { Typography,Box } from '@mui/material';
import { formatAgeInDays } from '../../utils/formDate';
import { useNamespace } from '../../contexts/NamespaceContext';
import NamespaceFilter from '../../components/NamespaceFilter';
import SearchInput from '../../components/SearchInput';

const PersistantVolume: React.FC = () => {
  const { persistant, loading, error } = usePersistantVolume();
  const { selectedNamespaces} = useNamespace();
  const [searchTerm, setSearchTerm] = useState('');
 
  const columns: GridColDef[] = [
    { field: 'id', headerName: 'ID', width: 80 },
    { field: 'name', headerName: 'Name', width: 300 },
    { field: 'namespace', headerName: 'Namespace', width: 250 },
    { field: 'storageClass', headerName: 'Storage Class', width: 180 },
    { field: 'size', headerName: 'Size', width: 120 },
    { field: 'age', headerName: 'Age', width: 100 },
    {
        field: 'status',
        headerName: 'Status',
        width: 120,
        renderCell: (params) => {
          const status = params.value?.toLowerCase();
          const color = status === 'bound' ? 'green' : 'orange';
          return (
            <strong style={{ color, fontWeight: 'bold' }}>
              {params.value}
            </strong>
          );
        },
      }
      
  ];
  

  const filteredPersistant = useMemo(() => {
              if (!persistant) return [];
            
              let result = selectedNamespaces.includes('all')
                ? persistant
                : persistant.filter(pers => selectedNamespaces.includes(pers.metadata.namespace));
            
              if (searchTerm.trim() !== '') {
                const lowerSearch = searchTerm.toLowerCase();
                result = result.filter(pers => {
                    const persistant = {
                        name: pers.metadata.name,
                        namespace: pers.metadata.namespace,
                        storageClass: pers.spec.storageClassName || '',
                        size: pers.spec.resources.requests.storage,
                        status: pers.status.phase,
                        age: formatAgeInDays(pers.metadata.creationTimestamp),
                      };
                      
            
                  return Object.values(persistant).some(value => {
                    if (typeof value === 'string' || typeof value === 'number') {
                      return value.toString().toLowerCase().includes(lowerSearch);
                    }
                    return false;
                  });
                });
              }
            
              return result;
            }, [persistant, selectedNamespaces, searchTerm]);
            const rows = (filteredPersistant ?? []).map((pers, index) => {
                return {
                  id: index + 1,
                  name: pers.metadata.name,
                  namespace: pers.metadata.namespace || 'default',
                  storageClass: pers.spec.storageClassName || '',
                  size: pers.spec.resources.requests.storage,
                  status: pers.status.phase,
                  age: formatAgeInDays(pers.metadata.creationTimestamp),
                };
              });
              
  return (
    <>
    <Box className="p-2">
  <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center mb-4">
    <NamespaceFilter />
    <SearchInput value={searchTerm} onChange={setSearchTerm} />
  </div>
      {error ? (
        <Typography color="error">{error}</Typography>
      ) : (
        <DataTable
          rows={rows}
          columns={columns}
          loading={loading}
        />
      )}
      </Box>
    </>
  );
};

export default PersistantVolume;