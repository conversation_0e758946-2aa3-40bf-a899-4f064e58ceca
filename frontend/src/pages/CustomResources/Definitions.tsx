import React, { useState, useMemo } from 'react';
import DataTable from '../../components/DataTable';
import { GridColDef } from '@mui/x-data-grid';
import { useCustomResourceDefinitions } from '../../hooks/CustomResources/useCustomResourceDefinitions';
import { Typography, Box } from '@mui/material';
import { formatAgeInDays } from '../../utils/formDate';
import SearchInput from '../../components/SearchInput';

const Definitions: React.FC = () => {
  const { crds, loading, error } = useCustomResourceDefinitions();
  const [searchTerm, setSearchTerm] = useState('');

  const columns: GridColDef[] = [
    { field: 'id', headerName: 'ID', width: 100 },
    { field: 'resource', headerName: 'Resource', width: 250 },
    { field: 'group', headerName: 'Group', width: 250 },
    { field: 'version', headerName: 'Version', width: 150 },
    { field: 'scope', headerName: 'Scope', width: 150 },
    { field: 'age', headerName: 'Age', width: 150 },
  ];

  const filteredCRDs = useMemo(() => {
    if (!crds) return [];

    let result = crds;

    // Filter by search term
    if (searchTerm.trim() !== '') {
      const lowerSearch = searchTerm.toLowerCase();
      result = result.filter(crd => {
        const searchableFields = {
          resource: crd.spec.names.kind,
          group: crd.spec.group,
          version: crd.spec.versions.find(v => v.storage)?.name || '',
          scope: crd.spec.scope,
          age: formatAgeInDays(crd.metadata.creationTimestamp),
        };

        return Object.values(searchableFields).some(value => {
          if (typeof value === 'string' || typeof value === 'number') {
            return value.toString().toLowerCase().includes(lowerSearch);
          }
          return false;
        });
      });
    }

    return result;
  }, [crds, searchTerm]);

  const rows = (filteredCRDs ?? []).map((crd, index) => ({
    id: index + 1,
    resource: crd.spec.names.kind,
    group: crd.spec.group,
    version: crd.spec.versions.find(v => v.storage)?.name || '',
    scope: crd.spec.scope,
    age: formatAgeInDays(crd.metadata.creationTimestamp),
  }));

  return (
    <Box className="p-2">
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center mb-4">
        <SearchInput value={searchTerm} onChange={setSearchTerm} />
      </div>
      {error ? (
        <Typography color="error">{error}</Typography>
      ) : (
        <DataTable
          rows={rows}
          columns={columns}
          loading={loading}
        />
      )}
    </Box>
  );
};

export default Definitions;
