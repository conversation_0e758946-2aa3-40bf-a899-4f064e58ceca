import React, { useState, useMemo } from 'react';
import DataTable from '../../../components/DataTable';
import { GridColDef } from '@mui/x-data-grid';
import { useCertificates } from '../../../hooks/CustomResources/CertManager/useCertificates';
import { Typography, Box } from '@mui/material';
import { formatAgeInDays } from '../../../utils/formDate';
import { useNamespace } from '../../../contexts/NamespaceContext';
import NamespaceFilter from '../../../components/NamespaceFilter';
import SearchInput from '../../../components/SearchInput';

const Certificates: React.FC = () => {
  const { certificates, loading, error } = useCertificates();
  const { selectedNamespaces } = useNamespace();
  const [searchTerm, setSearchTerm] = useState('');

  const columns: GridColDef[] = [
    { field: 'id', headerName: 'ID', width: 100 },
    { field: 'name', headerName: 'Name', width: 250 },
    { field: 'namespace', headerName: 'Namespace', width: 150 },
    { field: 'ready', headerName: 'Ready', width: 120 },
    { field: 'secret', headerName: 'Secret', width: 200 },
    { field: 'age', headerName: 'Age', width: 150 },
  ];

  const filteredCertificates = useMemo(() => {
    if (!certificates) return [];

    let result = certificates;

    // Filter by namespace if not showing all namespaces
    if (selectedNamespaces.length > 0 && !selectedNamespaces.includes('all')) {
      result = result.filter(cert => 
        selectedNamespaces.includes(cert.metadata.namespace)
      );
    }

    // Filter by search term
    if (searchTerm.trim() !== '') {
      const lowerSearch = searchTerm.toLowerCase();
      result = result.filter(cert => {
        const isReady = cert.status?.conditions?.some(
          condition => condition.type === 'Ready' && condition.status === 'True'
        ) ? 'True' : 'False';
        
        const searchableFields = {
          name: cert.metadata.name,
          namespace: cert.metadata.namespace,
          ready: isReady,
          secret: cert.spec.secretName,
          age: formatAgeInDays(cert.metadata.creationTimestamp),
        };

        return Object.values(searchableFields).some(value => {
          if (typeof value === 'string' || typeof value === 'number') {
            return value.toString().toLowerCase().includes(lowerSearch);
          }
          return false;
        });
      });
    }

    return result;
  }, [certificates, selectedNamespaces, searchTerm]);

  const rows = (filteredCertificates ?? []).map((cert, index) => {
    const isReady = cert.status?.conditions?.some(
      condition => condition.type === 'Ready' && condition.status === 'True'
    ) ? 'True' : 'False';
    
    return {
      id: index + 1,
      name: cert.metadata.name,
      namespace: cert.metadata.namespace,
      ready: isReady,
      secret: cert.spec.secretName,
      age: formatAgeInDays(cert.metadata.creationTimestamp),
    };
  });

  return (
    <Box className="p-2">
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center mb-4">
        <NamespaceFilter />
        <SearchInput value={searchTerm} onChange={setSearchTerm} />
      </div>
      {error ? (
        <Typography color="error">{error}</Typography>
      ) : (
        <DataTable
          rows={rows}
          columns={columns}
          loading={loading}
        />
      )}
    </Box>
  );
};

export default Certificates;
