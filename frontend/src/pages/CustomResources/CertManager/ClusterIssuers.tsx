import React, { useState, useMemo } from 'react';
import DataTable from '../../../components/DataTable';
import { GridColDef } from '@mui/x-data-grid';
import { useClusterIssuers } from '../../../hooks/CustomResources/CertManager/useClusterIssuers';
import { Typography, Box } from '@mui/material';
import { formatAgeInDays } from '../../../utils/formDate';
import SearchInput from '../../../components/SearchInput';

const ClusterIssuers: React.FC = () => {
  const { clusterIssuers, loading, error } = useClusterIssuers();
  const [searchTerm, setSearchTerm] = useState('');

  const columns: GridColDef[] = [
    { field: 'id', headerName: 'ID', width: 100 },
    { field: 'name', headerName: 'Name', width: 300 },
    { field: 'ready', headerName: 'Ready', width: 150 },
    { field: 'age', headerName: 'Age', width: 150 },
  ];

  const filteredClusterIssuers = useMemo(() => {
    if (!clusterIssuers) return [];

    let result = clusterIssuers;

    // Filter by search term
    if (searchTerm.trim() !== '') {
      const lowerSearch = searchTerm.toLowerCase();
      result = result.filter(issuer => {
        const isReady = issuer.status?.conditions?.some(
          condition => condition.type === 'Ready' && condition.status === 'True'
        ) ? 'True' : 'False';
        
        const searchableFields = {
          name: issuer.metadata.name,
          ready: isReady,
          age: formatAgeInDays(issuer.metadata.creationTimestamp),
        };

        return Object.values(searchableFields).some(value => {
          if (typeof value === 'string' || typeof value === 'number') {
            return value.toString().toLowerCase().includes(lowerSearch);
          }
          return false;
        });
      });
    }

    return result;
  }, [clusterIssuers, searchTerm]);

  const rows = (filteredClusterIssuers ?? []).map((issuer, index) => {
    const isReady = issuer.status?.conditions?.some(
      condition => condition.type === 'Ready' && condition.status === 'True'
    ) ? 'True' : 'False';
    
    return {
      id: index + 1,
      name: issuer.metadata.name,
      ready: isReady,
      age: formatAgeInDays(issuer.metadata.creationTimestamp),
    };
  });

  return (
    <Box className="p-2">
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center mb-4">
        <SearchInput value={searchTerm} onChange={setSearchTerm} />
      </div>
      {error ? (
        <Typography color="error">{error}</Typography>
      ) : (
        <DataTable
          rows={rows}
          columns={columns}
          loading={loading}
        />
      )}
    </Box>
  );
};

export default ClusterIssuers;
