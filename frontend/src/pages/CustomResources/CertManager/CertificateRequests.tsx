import React, { useState, useMemo } from 'react';
import DataTable from '../../../components/DataTable';
import { GridColDef } from '@mui/x-data-grid';
import { useCertificateRequests } from '../../../hooks/CustomResources/CertManager/useCertificateRequests';
import { Typography, Box } from '@mui/material';
import { formatAgeInDays } from '../../../utils/formDate';
import { useNamespace } from '../../../contexts/NamespaceContext';
import NamespaceFilter from '../../../components/NamespaceFilter';
import SearchInput from '../../../components/SearchInput';

const CertificateRequests: React.FC = () => {
  const { certificateRequests, loading, error } = useCertificateRequests();
  const { selectedNamespaces } = useNamespace();
  const [searchTerm, setSearchTerm] = useState('');

  const columns: GridColDef[] = [
    { field: 'id', headerName: 'ID', width: 100 },
    { field: 'name', headerName: 'Name', width: 200 },
    { field: 'namespace', headerName: 'Namespace', width: 120 },
    { field: 'approved', headerName: 'Approved', width: 120 },
    { field: 'denied', headerName: 'Denied', width: 120 },
    { field: 'ready', headerName: 'Ready', width: 120 },
    { field: 'issuer', headerName: 'Issuer', width: 150 },
    { field: 'requester', headerName: 'Requester', width: 150 },
    { field: 'age', headerName: 'Age', width: 120 },
  ];

  const filteredCertificateRequests = useMemo(() => {
    if (!certificateRequests) return [];

    let result = certificateRequests;

    // Filter by namespace if not showing all namespaces
    if (selectedNamespaces.length > 0 && !selectedNamespaces.includes('all')) {
      result = result.filter(cr => 
        selectedNamespaces.includes(cr.metadata.namespace)
      );
    }

    // Filter by search term
    if (searchTerm.trim() !== '') {
      const lowerSearch = searchTerm.toLowerCase();
      result = result.filter(cr => {
        const isApproved = cr.status?.conditions?.some(
          condition => condition.type === 'Approved' && condition.status === 'True'
        ) ? 'True' : 'False';
        
        const isDenied = cr.status?.conditions?.some(
          condition => condition.type === 'Denied' && condition.status === 'True'
        ) ? 'True' : 'False';
        
        const isReady = cr.status?.conditions?.some(
          condition => condition.type === 'Ready' && condition.status === 'True'
        ) ? 'True' : 'False';
        
        const searchableFields = {
          name: cr.metadata.name,
          namespace: cr.metadata.namespace,
          approved: isApproved,
          denied: isDenied,
          ready: isReady,
          issuer: `${cr.spec.issuerRef.kind}/${cr.spec.issuerRef.name}`,
          requester: cr.spec.username || 'System',
          age: formatAgeInDays(cr.metadata.creationTimestamp),
        };

        return Object.values(searchableFields).some(value => {
          if (typeof value === 'string' || typeof value === 'number') {
            return value.toString().toLowerCase().includes(lowerSearch);
          }
          return false;
        });
      });
    }

    return result;
  }, [certificateRequests, selectedNamespaces, searchTerm]);

  const rows = (filteredCertificateRequests ?? []).map((cr, index) => {
    const isApproved = cr.status?.conditions?.some(
      condition => condition.type === 'Approved' && condition.status === 'True'
    ) ? 'True' : 'False';
    
    const isDenied = cr.status?.conditions?.some(
      condition => condition.type === 'Denied' && condition.status === 'True'
    ) ? 'True' : 'False';
    
    const isReady = cr.status?.conditions?.some(
      condition => condition.type === 'Ready' && condition.status === 'True'
    ) ? 'True' : 'False';
    
    return {
      id: index + 1,
      name: cr.metadata.name,
      namespace: cr.metadata.namespace,
      approved: isApproved,
      denied: isDenied,
      ready: isReady,
      issuer: `${cr.spec.issuerRef.kind}/${cr.spec.issuerRef.name}`,
      requester: cr.spec.username || 'System',
      age: formatAgeInDays(cr.metadata.creationTimestamp),
    };
  });

  return (
    <Box className="p-2">
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center mb-4">
        <NamespaceFilter />
        <SearchInput value={searchTerm} onChange={setSearchTerm} />
      </div>
      {error ? (
        <Typography color="error">{error}</Typography>
      ) : (
        <DataTable
          rows={rows}
          columns={columns}
          loading={loading}
        />
      )}
    </Box>
  );
};

export default CertificateRequests;
