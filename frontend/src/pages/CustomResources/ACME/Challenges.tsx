import React, { useState, useMemo } from 'react';
import DataTable from '../../../components/DataTable';
import { GridColDef } from '@mui/x-data-grid';
import { useChallenges } from '../../../hooks/CustomResources/ACME/useChallenges';
import { Typography, Box } from '@mui/material';
import { formatAgeInDays } from '../../../utils/formDate';
import { useNamespace } from '../../../contexts/NamespaceContext';
import NamespaceFilter from '../../../components/NamespaceFilter';
import SearchInput from '../../../components/SearchInput';

const Challenges: React.FC = () => {
  const { challenges, loading, error } = useChallenges();
  const { selectedNamespaces } = useNamespace();
  const [searchTerm, setSearchTerm] = useState('');

  const columns: GridColDef[] = [
    { field: 'id', headerName: 'ID', width: 100 },
    { field: 'name', headerName: 'Name', width: 250 },
    { field: 'namespace', headerName: 'Namespace', width: 150 },
    { field: 'state', headerName: 'State', width: 150 },
    { field: 'domain', headerName: 'Domain', width: 250 },
    { field: 'age', headerName: 'Age', width: 150 },
  ];

  const filteredChallenges = useMemo(() => {
    if (!challenges) return [];

    let result = challenges;

    // Filter by namespace if not showing all namespaces
    if (selectedNamespaces.length > 0 && !selectedNamespaces.includes('all')) {
      result = result.filter(challenge => 
        selectedNamespaces.includes(challenge.metadata.namespace)
      );
    }

    // Filter by search term
    if (searchTerm.trim() !== '') {
      const lowerSearch = searchTerm.toLowerCase();
      result = result.filter(challenge => {
        const searchableFields = {
          name: challenge.metadata.name,
          namespace: challenge.metadata.namespace,
          state: challenge.status.state,
          domain: challenge.spec.dnsName,
          age: formatAgeInDays(challenge.metadata.creationTimestamp),
        };

        return Object.values(searchableFields).some(value => {
          if (typeof value === 'string' || typeof value === 'number') {
            return value.toString().toLowerCase().includes(lowerSearch);
          }
          return false;
        });
      });
    }

    return result;
  }, [challenges, selectedNamespaces, searchTerm]);

  const rows = (filteredChallenges ?? []).map((challenge, index) => ({
    id: index + 1,
    name: challenge.metadata.name,
    namespace: challenge.metadata.namespace,
    state: challenge.status.state,
    domain: challenge.spec.dnsName,
    age: formatAgeInDays(challenge.metadata.creationTimestamp),
  }));

  return (
    <Box className="p-2">
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center mb-4">
        <NamespaceFilter />
        <SearchInput value={searchTerm} onChange={setSearchTerm} />
      </div>
      {error ? (
        <Typography color="error">{error}</Typography>
      ) : (
        <DataTable
          rows={rows}
          columns={columns}
          loading={loading}
        />
      )}
    </Box>
  );
};

export default Challenges;
