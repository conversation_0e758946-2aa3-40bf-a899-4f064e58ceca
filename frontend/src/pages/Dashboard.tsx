import React from 'react';
import {
  Box,
  Grid,
  Typography,
  Paper,
  Divider,
} from '@mui/material';
import {
  Storage as StorageIcon,
  Memory as MemoryIcon,
  DeveloperBoard as NodeIcon,
  SettingsInputComponent as PodIcon,
} from '@mui/icons-material';
import useSelectedCluster from '../hooks/cluster/useSelectedCluster';

const StatCard = ({
  icon,
  title,
  value,
  unit,
  color = '#1976d2',
}: {
  icon: React.ReactNode;
  title: string;
  value: string | number;
  unit?: string;
  color?: string;
}) => (
  <Paper elevation={3} sx={{ p: 3, borderRadius: 3 }}>
    <Box display="flex" alignItems="center" gap={2}>
      <Box
        sx={{
          p: 2,
          backgroundColor: color,
          color: 'white',
          borderRadius: 2,
        }}
      >
        {icon}
      </Box>
      <Box>
        <Typography variant="subtitle2" color="text.secondary">
          {title}
        </Typography>
        <Typography variant="h5">
          {value}
          {unit && <Typography variant="body2" component="span"> {unit}</Typography>}
        </Typography>
      </Box>
    </Box>
  </Paper>
);

const Dashboard: React.FC = () => {
  const [selectedCluster] = useSelectedCluster();

  return (
    <Box sx={{ p: 4, mt: 8 }}>
      <Typography variant="h4" fontWeight={600} gutterBottom>
        {selectedCluster ? `${selectedCluster} Cluster Overview` : 'Cluster Overview'}
      </Typography>

      <Grid container spacing={4}>
        {/* Example stat cards */}
        <Grid>
          <StatCard icon={<NodeIcon />} title="Nodes Healthy" value={4} unit="/ 4" color="#4caf50" />
        </Grid>
        <Grid>
          <StatCard icon={<PodIcon />} title="Pods Running" value={58} unit="/ 60" color="#2196f3" />
        </Grid>
        <Grid>
          <StatCard icon={<MemoryIcon />} title="Memory Usage" value="7.2" unit="GB / 16 GB" color="#ff9800" />
        </Grid>
        <Grid>
          <StatCard icon={<StorageIcon />} title="CPU Usage" value="3.1" unit="Cores / 8" color="#e91e63" />
        </Grid>
      </Grid>

      <Divider sx={{ my: 5 }} />

      <Typography variant="h5" fontWeight={500} gutterBottom>
        Workloads Summary
      </Typography>
      <Grid container spacing={4}>
        <Grid >
          <StatCard icon={<PodIcon />} title="Deployments" value={12} color="#673ab7" />
        </Grid>
        <Grid>
          <StatCard icon={<PodIcon />} title="DaemonSets" value={5} color="#3f51b5" />
        </Grid>
        <Grid>
          <StatCard icon={<PodIcon />} title="StatefulSets" value={3} color="#009688" />
        </Grid>
        <Grid>
          <StatCard icon={<PodIcon />} title="CronJobs" value={6} color="#795548" />
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
