import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useClusters, ClusterInfo } from '../hooks/cluster/useClusters';
import useSelectedCluster from '../hooks/cluster/useSelectedCluster';
import ClusterUploadForm from '../components/ClusterUploadForm';
import { formatDistanceToNow } from 'date-fns';

import {
  Grid,
  Card,
  CardContent,
  CardActionArea,
  Typography,
  Box,
  CircularProgress,
  Paper,
  Button,
  Collapse,
} from '@mui/material';

const Cluster: React.FC = () => {
  const { clusters, loading, error, refetch } = useClusters();
  const [selectedCluster, setSelectedCluster] = useSelectedCluster();
  const [showUploadForm, setShowUploadForm] = useState(false);
  const navigate = useNavigate();

  const handleSelectCluster = (cluster: ClusterInfo) => {
    setSelectedCluster(cluster.name);
    navigate('/dashboard');
  };

  const handleUploadSuccess = () => {
    refetch();
    setShowUploadForm(false);
  };

  if (loading)
    return (
      <Box display="flex" justifyContent="center" mt={5}>
        <CircularProgress />
      </Box>
    );

  if (error)
    return (
      <Box textAlign="center" mt={5}>
        <Typography variant="h6" color="error">Error: {error}</Typography>
        <Button
          variant="contained"
          color="primary"
          onClick={refetch}
          sx={{ mt: 2 }}
        >
          Try Again
        </Button>
      </Box>
    );

  return (
    <Box p={4}>
      <Paper elevation={3} sx={{ padding: 4, mb: 4 }}>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <div>
            <Typography variant="h4" gutterBottom fontWeight="bold">
              Select Your Kubernetes Cluster
            </Typography>
            <Typography variant="body1" color="textSecondary">
              Please select one of the clusters below to monitor its metrics, pods,
              and health. You'll be redirected to the dashboard once selected.
            </Typography>
          </div>
          <Button
            variant="contained"
            color="primary"
            onClick={() => setShowUploadForm(!showUploadForm)}
          >
            {showUploadForm ? 'Hide Upload Form' : 'Upload New Cluster'}
          </Button>
        </Box>
      </Paper>

      <Collapse in={showUploadForm}>
        <ClusterUploadForm onUploadSuccess={handleUploadSuccess} />
      </Collapse>

      {clusters.length === 0 ? (
        <Typography>No clusters found.</Typography>
      ) : (
        <Grid container spacing={4} justifyContent="center">
          {clusters.map((cluster, index) => {
            const isSelected = selectedCluster === cluster.name;

            return (
              <Grid key={index}>
                <Card
                  sx={{
                    width: 300,
                    height: 200,
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    border: isSelected ? '3px solid #1976d2' : '1px solid #ddd',
                    backgroundColor: isSelected ? '#e3f2fd' : '#fafafa',
                    transition: '0.3s',
                    boxShadow: isSelected ? 6 : 2,
                    '&:hover': {
                      boxShadow: 6,
                    },
                  }}
                >
                  <CardActionArea
                    sx={{ height: '100%' }}
                    onClick={() => handleSelectCluster(cluster)}
                  >
                    <CardContent
                      sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        justifyContent: 'center',
                        alignItems: 'center',
                        height: '100%',
                      }}
                    >
                      <Typography variant="h6" fontWeight="bold">
                        {cluster.name}
                      </Typography>
                      <Typography
                        variant="body2"
                        color="textSecondary"
                        mt={1}
                      >
                        Uploaded {formatDistanceToNow(new Date(cluster.upload_time))} ago
                      </Typography>
                    </CardContent>
                  </CardActionArea>
                </Card>
              </Grid>
            );
          })}
        </Grid>
      )}
    </Box>
  );
};

export default Cluster;
