import React, { useState, useMemo } from 'react';
import DataTable from '../../components/DataTable';
import { GridColDef } from '@mui/x-data-grid';
import { useHelmReleases } from '../../hooks/Helm/useHelmReleases';
import { Typography, Box } from '@mui/material';
import { formatAgeInDays } from '../../utils/formDate';
import { useNamespace } from '../../contexts/NamespaceContext';
import NamespaceFilter from '../../components/NamespaceFilter';
import SearchInput from '../../components/SearchInput';

const HelmReleases: React.FC = () => {
  const { releases, loading, error } = useHelmReleases();
  const { selectedNamespaces } = useNamespace();
  const [searchTerm, setSearchTerm] = useState('');

  const columns: GridColDef[] = [
    { field: 'id', headerName: 'ID', width: 70 },
    { field: 'name', headerName: 'Name', width: 150 },
    { field: 'namespace', headerName: 'Namespace', width: 120 },
    { field: 'chart', headerName: 'Chart', width: 150 },
    { field: 'revision', headerName: 'Revision', width: 100, type: 'number' },
    { field: 'version', headerName: 'Version', width: 120 },
    { field: 'appVersion', headerName: 'App Version', width: 120 },
    { 
      field: 'status', 
      headerName: 'Status', 
      width: 120,
      renderCell: (params) => {
        const status = params.value;
        const isDeployed = status === 'deployed';
        return (
          <Box sx={{ display: 'flex', alignItems: 'center', width:'100%', height:'100%', gap: 1 }}>
            <Box
              sx={{
                width: 10,
                height: 10,
                borderRadius: '50%',
                backgroundColor: isDeployed ? '#4caf50' : '#f44336', // green or red
              }}
            />
            <span>{status}</span>
          </Box>
        );
      }
    },
    { field: 'updated', headerName: 'Updated', width: 150 },
  ];

  const filteredReleases = useMemo(() => {
    if (!releases) return [];

    let result = releases;

    // Filter by namespace if not "all" namespaces selected
    if (!selectedNamespaces.includes('all')) {
      result = result.filter(release => selectedNamespaces.includes(release.namespace));
    }

    // Filter by search term
    if (searchTerm.trim() !== '') {
      const lowerSearch = searchTerm.toLowerCase();
      result = result.filter(release => {
        const releaseData = {
          name: release.name,
          namespace: release.namespace,
          chart: release.chart,
          revision: release.revision,
          version: release.version,
          appVersion: release.app_version,
          status: release.status,
          updated: release.updated,
        };

        return Object.values(releaseData).some(value => {
          if (typeof value === 'string' || typeof value === 'number') {
            return value.toString().toLowerCase().includes(lowerSearch);
          }
          return false;
        });
      });
    }

    return result;
  }, [releases, selectedNamespaces, searchTerm]);

  const rows = (filteredReleases ?? []).map((release, index) => ({
    id: index + 1,
    name: release.name,
    namespace: release.namespace,
    chart: release.chart,
    revision: release.revision,
    version: release.version,
    appVersion: release.app_version,
    status: release.status,
    updated: formatAgeInDays(release.updated),
  }));

  return (
    <Box className="p-2">
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center mb-4">
        <NamespaceFilter />
        <SearchInput value={searchTerm} onChange={setSearchTerm} />
      </div>
      {error ? (
        <Typography color="error">{error}</Typography>
      ) : (
        <DataTable
          rows={rows}
          columns={columns}
          loading={loading}
        />
      )}
    </Box>
  );
};

export default HelmReleases;
