import React, { useState, useMemo } from 'react';
import DataTable from '../../components/DataTable';
import { GridColDef } from '@mui/x-data-grid';
import { useHelmCharts } from '../../hooks/Helm/useHelmCharts';
import { Typography, Box } from '@mui/material';
import SearchInput from '../../components/SearchInput';

const HelmCharts: React.FC = () => {
  const { charts, loading, error } = useHelmCharts();
  const [searchTerm, setSearchTerm] = useState('');

  const columns: GridColDef[] = [
    { field: 'id', headerName: 'ID', width: 70 },
    { field: 'name', headerName: 'Name', width: 200 },
    { field: 'description', headerName: 'Description', width: 400 },
    { field: 'version', headerName: 'Version', width: 120 },
    { field: 'appVersion', headerName: 'App Version', width: 120 },
    { field: 'repository', headerName: 'Repository', width: 150 },
  ];

  const filteredCharts = useMemo(() => {
    if (!charts) return [];

    let result = charts;

    if (searchTerm.trim() !== '') {
      const lowerSearch = searchTerm.toLowerCase();
      result = result.filter(chart => {
        const chartData = {
          name: chart.name,
          description: chart.description,
          version: chart.version,
          appVersion: chart.appVersion,
          repository: chart.repository,
        };

        return Object.values(chartData).some(value => {
          if (typeof value === 'string' || typeof value === 'number') {
            return value.toString().toLowerCase().includes(lowerSearch);
          }
          return false;
        });
      });
    }

    return result;
  }, [charts, searchTerm]);

  const rows = (filteredCharts ?? []).map((chart, index) => ({
    id: index + 1,
    name: chart.name,
    description: chart.description,
    version: chart.version,
    appVersion: chart.appVersion,
    repository: chart.repository,
  }));

  return (
    <Box className="p-2">
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center mb-4">
        <SearchInput value={searchTerm} onChange={setSearchTerm} />
      </div>
      {error ? (
        <Typography color="error">{error}</Typography>
      ) : (
        <DataTable
          rows={rows}
          columns={columns}
          loading={loading}
        />
      )}
    </Box>
  );
};

export default HelmCharts;
