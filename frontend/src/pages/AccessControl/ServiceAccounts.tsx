import React, { useState, useMemo } from 'react';
import DataTable from '../../components/DataTable';
import { GridColDef } from '@mui/x-data-grid';
import { useServiceAccounts } from '../../hooks/AccessControl/useServiceAccounts';
import { Typography, Box } from '@mui/material';
import { formatAgeInDays } from '../../utils/formDate';
import { useNamespace } from '../../contexts/NamespaceContext';
import NamespaceFilter from '../../components/NamespaceFilter';
import SearchInput from '../../components/SearchInput';

const ServiceAccounts: React.FC = () => {
  const { serviceAccounts, loading, error } = useServiceAccounts();
  const { selectedNamespaces } = useNamespace();
  const [searchTerm, setSearchTerm] = useState('');

  const columns: GridColDef[] = [
    { field: 'id', headerName: 'ID', width: 100 },
    { field: 'name', headerName: 'Name', width: 300 },
    { field: 'namespace', headerName: 'Namespace', width: 150 },
    { field: 'age', headerName: 'Age', width: 150 },
  ];

  const filteredServiceAccounts = useMemo(() => {
    if (!serviceAccounts) return [];

    let result = serviceAccounts;

    // Filter by namespace if not showing all namespaces
    if (selectedNamespaces.length > 0 && !selectedNamespaces.includes('all')) {
      result = result.filter(sa => 
        selectedNamespaces.includes(sa.metadata.namespace)
      );
    }

    // Filter by search term
    if (searchTerm.trim() !== '') {
      const lowerSearch = searchTerm.toLowerCase();
      result = result.filter(sa => {
        const searchableFields = {
          name: sa.metadata.name,
          namespace: sa.metadata.namespace,
          age: formatAgeInDays(sa.metadata.creationTimestamp),
        };

        return Object.values(searchableFields).some(value => {
          if (typeof value === 'string' || typeof value === 'number') {
            return value.toString().toLowerCase().includes(lowerSearch);
          }
          return false;
        });
      });
    }

    return result;
  }, [serviceAccounts, selectedNamespaces, searchTerm]);

  const rows = (filteredServiceAccounts ?? []).map((sa, index) => ({
    id: index + 1,
    name: sa.metadata.name,
    namespace: sa.metadata.namespace || 'default',
    age: formatAgeInDays(sa.metadata.creationTimestamp),
  }));

  return (
    <Box className="p-2">
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center mb-4">
        <NamespaceFilter />
        <SearchInput value={searchTerm} onChange={setSearchTerm} />
      </div>
      {error ? (
        <Typography color="error">{error}</Typography>
      ) : (
        <DataTable
          rows={rows}
          columns={columns}
          loading={loading}
        />
      )}
    </Box>
  );
};

export default ServiceAccounts;
