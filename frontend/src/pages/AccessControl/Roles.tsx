import React, { useState, useMemo } from 'react';
import DataTable from '../../components/DataTable';
import { GridColDef } from '@mui/x-data-grid';
import { useRoles } from '../../hooks/AccessControl/useRoles';
import { Typography, Box } from '@mui/material';
import { formatAgeInDays } from '../../utils/formDate';
import { useNamespace } from '../../contexts/NamespaceContext';
import NamespaceFilter from '../../components/NamespaceFilter';
import SearchInput from '../../components/SearchInput';

const Roles: React.FC = () => {
  const { roles, loading, error } = useRoles();
  const { selectedNamespaces } = useNamespace();
  const [searchTerm, setSearchTerm] = useState('');

  const columns: GridColDef[] = [
    { field: 'id', headerName: 'ID', width: 100 },
    { field: 'name', headerName: 'Name', width: 300 },
    { field: 'namespace', headerName: 'Namespace', width: 150 },
    { field: 'age', headerName: 'Age', width: 150 },
  ];

  const filteredRoles = useMemo(() => {
    if (!roles) return [];

    let result = roles;

    // Filter by namespace if not showing all namespaces
    if (selectedNamespaces.length > 0 && !selectedNamespaces.includes('all')) {
      result = result.filter(role => 
        selectedNamespaces.includes(role.metadata.namespace)
      );
    }

    // Filter by search term
    if (searchTerm.trim() !== '') {
      const lowerSearch = searchTerm.toLowerCase();
      result = result.filter(role => {
        const searchableFields = {
          name: role.metadata.name,
          namespace: role.metadata.namespace,
          age: formatAgeInDays(role.metadata.creationTimestamp),
        };

        return Object.values(searchableFields).some(value => {
          if (typeof value === 'string' || typeof value === 'number') {
            return value.toString().toLowerCase().includes(lowerSearch);
          }
          return false;
        });
      });
    }

    return result;
  }, [roles, selectedNamespaces, searchTerm]);

  const rows = (filteredRoles ?? []).map((role, index) => ({
    id: index + 1,
    name: role.metadata.name,
    namespace: role.metadata.namespace || 'default',
    age: formatAgeInDays(role.metadata.creationTimestamp),
  }));

  return (
    <Box className="p-2">
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center mb-4">
        <NamespaceFilter />
        <SearchInput value={searchTerm} onChange={setSearchTerm} />
      </div>
      {error ? (
        <Typography color="error">{error}</Typography>
      ) : (
        <DataTable
          rows={rows}
          columns={columns}
          loading={loading}
        />
      )}
    </Box>
  );
};

export default Roles;
