import React, { useState, useMemo } from 'react';
import DataTable from '../../components/DataTable';
import { GridColDef } from '@mui/x-data-grid';
import { useClusterRoleBindings } from '../../hooks/AccessControl/useClusterRoleBindings';
import { Typography, Box } from '@mui/material';
import { formatAgeInDays } from '../../utils/formDate';
import SearchInput from '../../components/SearchInput';

const ClusterRoleBindings: React.FC = () => {
  const { clusterRoleBindings, loading, error } = useClusterRoleBindings();
  const [searchTerm, setSearchTerm] = useState('');

  const columns: GridColDef[] = [
    { field: 'id', headerName: 'ID', width: 100 },
    { field: 'name', headerName: 'Name', width: 300 },
    { field: 'bindings', headerName: 'Bindings', width: 400 },
    { field: 'age', headerName: 'Age', width: 150 },
  ];

  const filteredClusterRoleBindings = useMemo(() => {
    if (!clusterRoleBindings) return [];

    let result = clusterRoleBindings;

    // Filter by search term
    if (searchTerm.trim() !== '') {
      const lowerSearch = searchTerm.toLowerCase();
      result = result.filter(binding => {
        const searchableFields = {
          name: binding.metadata.name,
          age: formatAgeInDays(binding.metadata.creationTimestamp),
          bindings: binding.subjects ? binding.subjects.map(s => `${s.kind}/${s.name}`).join(', ') : '',
        };

        return Object.values(searchableFields).some(value => {
          if (typeof value === 'string' || typeof value === 'number') {
            return value.toString().toLowerCase().includes(lowerSearch);
          }
          return false;
        });
      });
    }

    return result;
  }, [clusterRoleBindings, searchTerm]);

  const rows = (filteredClusterRoleBindings ?? []).map((binding, index) => ({
    id: index + 1,
    name: binding.metadata.name,
    bindings: binding.subjects 
      ? binding.subjects.map(s => `${s.kind}/${s.name}`).join(', ') 
      : 'No bindings',
    age: formatAgeInDays(binding.metadata.creationTimestamp),
  }));

  return (
    <Box className="p-2">
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center mb-4">
        <SearchInput value={searchTerm} onChange={setSearchTerm} />
      </div>
      {error ? (
        <Typography color="error">{error}</Typography>
      ) : (
        <DataTable
          rows={rows}
          columns={columns}
          loading={loading}
        />
      )}
    </Box>
  );
};

export default ClusterRoleBindings;
