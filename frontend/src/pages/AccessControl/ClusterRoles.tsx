import React, { useState, useMemo } from 'react';
import DataTable from '../../components/DataTable';
import { GridColDef } from '@mui/x-data-grid';
import { useClusterRoles } from '../../hooks/AccessControl/useClusterRoles';
import { Typography, Box } from '@mui/material';
import { formatAgeInDays } from '../../utils/formDate';
import SearchInput from '../../components/SearchInput';

const ClusterRoles: React.FC = () => {
  const { clusterRoles, loading, error } = useClusterRoles();
  const [searchTerm, setSearchTerm] = useState('');

  const columns: GridColDef[] = [
    { field: 'id', headerName: 'ID', width: 100 },
    { field: 'name', headerName: 'Name', width: 400 },
    { field: 'age', headerName: 'Age', width: 150 },
  ];

  const filteredClusterRoles = useMemo(() => {
    if (!clusterRoles) return [];

    let result = clusterRoles;

    // Filter by search term
    if (searchTerm.trim() !== '') {
      const lowerSearch = searchTerm.toLowerCase();
      result = result.filter(role => {
        const searchableFields = {
          name: role.metadata.name,
          age: formatAgeInDays(role.metadata.creationTimestamp),
        };

        return Object.values(searchableFields).some(value => {
          if (typeof value === 'string' || typeof value === 'number') {
            return value.toString().toLowerCase().includes(lowerSearch);
          }
          return false;
        });
      });
    }

    return result;
  }, [clusterRoles, searchTerm]);

  const rows = (filteredClusterRoles ?? []).map((role, index) => ({
    id: index + 1,
    name: role.metadata.name,
    age: formatAgeInDays(role.metadata.creationTimestamp),
  }));

  return (
    <Box className="p-2">
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center mb-4">
        <SearchInput value={searchTerm} onChange={setSearchTerm} />
      </div>
      {error ? (
        <Typography color="error">{error}</Typography>
      ) : (
        <DataTable
          rows={rows}
          columns={columns}
          loading={loading}
        />
      )}
    </Box>
  );
};

export default ClusterRoles;
