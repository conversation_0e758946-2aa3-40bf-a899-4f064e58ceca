import React, { useState, useMemo } from 'react';
import DataTable from '../../components/DataTable';
import { GridColDef } from '@mui/x-data-grid';
import { useRoleBindings } from '../../hooks/AccessControl/useRoleBindings';
import { Typography, Box } from '@mui/material';
import { formatAgeInDays } from '../../utils/formDate';
import { useNamespace } from '../../contexts/NamespaceContext';
import NamespaceFilter from '../../components/NamespaceFilter';
import SearchInput from '../../components/SearchInput';

const RoleBindings: React.FC = () => {
  const { roleBindings, loading, error } = useRoleBindings();
  const { selectedNamespaces } = useNamespace();
  const [searchTerm, setSearchTerm] = useState('');

  const columns: GridColDef[] = [
    { field: 'id', headerName: 'ID', width: 100 },
    { field: 'name', headerName: 'Name', width: 250 },
    { field: 'namespace', headerName: 'Namespace', width: 150 },
    { field: 'bindings', headerName: 'Bindings', width: 350 },
    { field: 'age', headerName: 'Age', width: 150 },
  ];

  const filteredRoleBindings = useMemo(() => {
    if (!roleBindings) return [];

    let result = roleBindings;

    // Filter by namespace if not showing all namespaces
    if (selectedNamespaces.length > 0 && !selectedNamespaces.includes('all')) {
      result = result.filter(binding => 
        selectedNamespaces.includes(binding.metadata.namespace)
      );
    }

    // Filter by search term
    if (searchTerm.trim() !== '') {
      const lowerSearch = searchTerm.toLowerCase();
      result = result.filter(binding => {
        const searchableFields = {
          name: binding.metadata.name,
          namespace: binding.metadata.namespace,
          age: formatAgeInDays(binding.metadata.creationTimestamp),
          bindings: binding.subjects ? binding.subjects.map(s => `${s.kind}/${s.name}`).join(', ') : '',
        };

        return Object.values(searchableFields).some(value => {
          if (typeof value === 'string' || typeof value === 'number') {
            return value.toString().toLowerCase().includes(lowerSearch);
          }
          return false;
        });
      });
    }

    return result;
  }, [roleBindings, selectedNamespaces, searchTerm]);

  const rows = (filteredRoleBindings ?? []).map((binding, index) => ({
    id: index + 1,
    name: binding.metadata.name,
    namespace: binding.metadata.namespace || 'default',
    bindings: binding.subjects 
      ? binding.subjects.map(s => `${s.kind}/${s.name}`).join(', ') 
      : 'No bindings',
    age: formatAgeInDays(binding.metadata.creationTimestamp),
  }));

  return (
    <Box className="p-2">
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center mb-4">
        <NamespaceFilter />
        <SearchInput value={searchTerm} onChange={setSearchTerm} />
      </div>
      {error ? (
        <Typography color="error">{error}</Typography>
      ) : (
        <DataTable
          rows={rows}
          columns={columns}
          loading={loading}
        />
      )}
    </Box>
  );
};

export default RoleBindings;
