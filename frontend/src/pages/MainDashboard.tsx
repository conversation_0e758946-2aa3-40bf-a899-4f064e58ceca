import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Grid,
  Typography,
  Paper,
  Card,
  CardContent,
  CardActionArea,
} from '@mui/material';
import {
  Storage as StorageIcon,
  Language as DomainIcon,
  Security as SecurityIcon,
  CloudQueue as ServerIcon,
  Speed as AvailabilityIcon,
  DeveloperBoard as ClusterIcon,
} from '@mui/icons-material';

const FeatureCard = ({
  icon,
  title,
  description,
  onClick,
  color = '#1976d2',
}: {
  icon: React.ReactNode;
  title: string;
  description: string;
  onClick: () => void;
  color?: string;
}) => (
  <Card
    sx={{
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      transition: 'transform 0.2s, box-shadow 0.2s',
      '&:hover': {
        transform: 'translateY(-5px)',
        boxShadow: '0 10px 20px rgba(0,0,0,0.1)',
      }
    }}
  >
    <CardActionArea
      sx={{
        flexGrow: 1,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'flex-start',
        p: 2
      }}
      onClick={onClick}
    >
      <Box
        sx={{
          p: 2,
          backgroundColor: color,
          color: 'white',
          borderRadius: 2,
          mb: 2
        }}
      >
        {icon}
      </Box>
      <CardContent sx={{ flexGrow: 1, width: '100%', p: 0, pt: 1 }}>
        <Typography variant="h5" component="h2" gutterBottom>
          {title}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          {description}
        </Typography>
      </CardContent>
    </CardActionArea>
  </Card>
);

const MainDashboard: React.FC = () => {
  const navigate = useNavigate();

  return (
    <Box sx={{ p: 4, mt: 2 }}>
      <Paper elevation={3} sx={{ p: 4, mb: 4 }}>
        <Typography variant="h4" fontWeight={600} gutterBottom>
          RedAlerts Dashboard
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Welcome to RedAlerts monitoring platform. Select a monitoring feature below to get started.
        </Typography>
      </Paper>

      <Grid container spacing={4}>
        <Grid>
          <FeatureCard
            icon={<ClusterIcon />}
            title="Kubernetes Clusters"
            description="Monitor and manage your Kubernetes clusters, pods, deployments, and other resources."
            onClick={() => navigate('/dashboard')}
            color="#e53935" // Red color to match theme
          />
        </Grid>

        <Grid>
          <FeatureCard
            icon={<SecurityIcon />}
            title="SSL Certificates"
            description="Monitor SSL certificate expiration dates and security status."
            onClick={() => {}}
            color="#757575" // Gray color for features to be implemented later
          />
        </Grid>

        <Grid>
          <FeatureCard
            icon={<DomainIcon />}
            title="Domain Expiration"
            description="Track domain name expiration dates and renewal status."
            onClick={() => {}}
            color="#757575"
          />
        </Grid>

        <Grid>
          <FeatureCard
            icon={<ServerIcon />}
            title="OVH Server Expiration"
            description="Monitor OVH server expiration dates and renewal status."
            onClick={() => {}}
            color="#757575"
          />
        </Grid>

        <Grid>
          <FeatureCard
            icon={<AvailabilityIcon />}
            title="Site Availability"
            description="Monitor website uptime, response time, and availability status."
            onClick={() => {}}
            color="#757575"
          />
        </Grid>

        <Grid>
          <FeatureCard
            icon={<StorageIcon />}
            title="Storage Monitoring"
            description="Track storage usage and capacity across your infrastructure."
            onClick={() => {}}
            color="#757575"
          />
        </Grid>
      </Grid>

      <Box sx={{ mt: 4, textAlign: 'center' }}>
        <Typography variant="body2" color="text.secondary">
          Note: Some features are currently under development and will be available soon.
        </Typography>
      </Box>
    </Box>
  );
};

export default MainDashboard;
