import React, {useState, useMemo} from 'react';
import DataTable from '../../components/DataTable';
import { GridColDef } from '@mui/x-data-grid';
import { useLimitRanges } from '../../hooks/Config/useLimitRanges';
import { Typography,Box } from '@mui/material';
import { formatAgeInDays } from '../../utils/formDate';
import { useNamespace } from '../../contexts/NamespaceContext';
import NamespaceFilter from '../../components/NamespaceFilter';
import SearchInput from '../../components/SearchInput';

const LimitRanges: React.FC = () => {
  const { limitRanges, loading, error } = useLimitRanges();
  const { selectedNamespaces} = useNamespace();
  const [searchTerm, setSearchTerm] = useState('');
 
  const columns: GridColDef[] = [
    { field: 'id', headerName: 'ID', width: 100 },
    { field: 'name', headerName: 'Name', width: 500 },
    { field: 'namespace', headerName: 'Namespace', width: 200 },
    { field: 'age', headerName: 'Age', width: 120 },
  ];

  const filteredLimitRanges = useMemo(() => {
              if (!limitRanges) return [];
            
              let result = selectedNamespaces.includes('all')
                ? limitRanges
                : limitRanges.filter(limit => selectedNamespaces.includes(limit.metadata.namespace));
            
              if (searchTerm.trim() !== '') {
                const lowerSearch = searchTerm.toLowerCase();
                result = result.filter(limit => {
                  const limitRanges = {
                    name: limit.metadata.name,
                    namespace: limit.metadata.namespace,
                    age: formatAgeInDays(limit.metadata.creationTimestamp),
                  };
            
                  return Object.values(limitRanges).some(value => {
                    if (typeof value === 'string' || typeof value === 'number') {
                      return value.toString().toLowerCase().includes(lowerSearch);
                    }
                    return false;
                  });
                });
              }
            
              return result;
            }, [limitRanges, selectedNamespaces, searchTerm]);
  const rows = (filteredLimitRanges ?? []).map((limit, index) => {
    
    return {
      id: index + 1,
      name: limit.metadata.name,
      namespace: limit.metadata.namespace || 'default',
      age: formatAgeInDays(limit.metadata.creationTimestamp),
    };
  });
  return (
    <>
    <Box className="p-2">
  <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center mb-4">
    <NamespaceFilter />
    <SearchInput value={searchTerm} onChange={setSearchTerm} />
  </div>
      {error ? (
        <Typography color="error">{error}</Typography>
      ) : (
        <DataTable
          rows={rows}
          columns={columns}
          loading={loading}
        />
      )}
      </Box>
    </>
  );
};

export default LimitRanges;