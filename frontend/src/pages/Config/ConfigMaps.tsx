import React, {useState, useMemo} from 'react';
import DataTable from '../../components/DataTable';
import { GridColDef } from '@mui/x-data-grid';
import { useConfigMaps } from '../../hooks/Config/useConfigMaps';
import { Typography,Box } from '@mui/material';
import { formatAgeInDays } from '../../utils/formDate';
import { useNamespace } from '../../contexts/NamespaceContext';
import NamespaceFilter from '../../components/NamespaceFilter';
import SearchInput from '../../components/SearchInput';

const ConfigMaps: React.FC = () => {
  const { configMaps, loading, error } = useConfigMaps();
  const { selectedNamespaces} = useNamespace();
  const [searchTerm, setSearchTerm] = useState('');
 
  const columns: GridColDef[] = [
    { field: 'id', headerName: 'ID', width: 100 },
    { field: 'name', headerName: 'Name', width: 500 },
    { field: 'namespace', headerName: 'Namespace', width: 200 },
    { field: 'Keys', headerName: 'key', width: 200 },
    { field: 'age', headerName: 'Age', width: 120 },
  ];

  const filteredConfig = useMemo(() => {
              if (!configMaps) return [];
            
              let result = selectedNamespaces.includes('all')
                ? configMaps
                : configMaps.filter(config => selectedNamespaces.includes(config.metadata.namespace));
            
              if (searchTerm.trim() !== '') {
                const lowerSearch = searchTerm.toLowerCase();
                result = result.filter(dep => {
                  const configMap = {
                    name: dep.metadata.name,
                    namespace: dep.metadata.namespace,
                    key: dep.data ? Object.keys(dep.data).join(', ') : '—',
                    age: formatAgeInDays(dep.metadata.creationTimestamp),
                  };
            
                  return Object.values(configMap).some(value => {
                    if (typeof value === 'string' || typeof value === 'number') {
                      return value.toString().toLowerCase().includes(lowerSearch);
                    }
                    return false;
                  });
                });
              }
            
              return result;
            }, [configMaps, selectedNamespaces, searchTerm]);
  const rows = (filteredConfig ?? []).map((configMap, index) => {
    const keys = configMap.data ? Object.keys(configMap.data).join(', ') : '—';
  
    return {
      id: index + 1,
      name: configMap.metadata.name,
      namespace: configMap.metadata.namespace || 'default',
      Keys: keys, // ici on injecte les clés comme ca.crt
      age: formatAgeInDays(configMap.metadata.creationTimestamp),
    };
  });
  return (
    <>
    <Box className="p-2">
  <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center mb-4">
    <NamespaceFilter />
    <SearchInput value={searchTerm} onChange={setSearchTerm} />
  </div>
      {error ? (
        <Typography color="error">{error}</Typography>
      ) : (
        <DataTable
          rows={rows}
          columns={columns}
          loading={loading}
        />
      )}
      </Box>
    </>
  );
};

export default ConfigMaps;