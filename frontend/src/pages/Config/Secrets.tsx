import React, {useState, useMemo} from 'react';
import DataTable from '../../components/DataTable';
import { GridColDef } from '@mui/x-data-grid';
import { useSecrets } from '../../hooks/Config/useSecrets';
import { Typography,Box } from '@mui/material';
import { formatAgeInDays } from '../../utils/formDate';
import { useNamespace } from '../../contexts/NamespaceContext';
import NamespaceFilter from '../../components/NamespaceFilter';
import SearchInput from '../../components/SearchInput';

const Secrets: React.FC = () => {
  const { secrets, loading, error } = useSecrets();
  const { selectedNamespaces} = useNamespace();
  const [searchTerm, setSearchTerm] = useState('');
 
  const columns: GridColDef[] = [
    { field: 'id', headerName: 'ID', width: 100 },
    { field: 'name', headerName: 'Name', width: 500 },
    { field: 'namespace', headerName: 'Namespace', width: 200 },
    { field: 'key', headerName: 'Keys', width: 200 },
    { field: 'labels', headerName: 'Labels', width: 400 },
    { field: 'age', headerName: 'Age', width: 120 },
  ];

  const filteredSecrets = useMemo(() => {
              if (!secrets) return [];
            
              let result = selectedNamespaces.includes('all')
                ? secrets
                : secrets.filter(secret => selectedNamespaces.includes(secret.metadata.namespace));
            
              if (searchTerm.trim() !== '') {
                const lowerSearch = searchTerm.toLowerCase();
                result = result.filter(secret => {
                  const configMap = {
                    name: secret.metadata.name,
                    namespace: secret.metadata.namespace,
                    key: secret.data ? Object.keys(secret.data).join(', ') : '—',
                    labels: secret.metadata.labels ? Object.entries(secret.metadata.labels).map(([key, value]) => `${key}: ${value}`).join(', ') : 'None',
                    age: formatAgeInDays(secret.metadata.creationTimestamp),
                  };
            
                  return Object.values(configMap).some(value => {
                    if (typeof value === 'string' || typeof value === 'number') {
                      return value.toString().toLowerCase().includes(lowerSearch);
                    }
                    return false;
                  });
                });
              }
            
              return result;
            }, [secrets, selectedNamespaces, searchTerm]);
  const rows = (filteredSecrets ?? []).map((secret, index) => {
    const keys = secret.data ? Object.keys(secret.data).join(', ') : '—';
    
    return {
      id: index + 1,
      name: secret.metadata.name,
      namespace: secret.metadata.namespace || 'default',
      key: keys,
      labels: secret.metadata.labels ? Object.entries(secret.metadata.labels).map(([key, value]) => `${key}: ${value}`).join(', ') : 'None',
      age: formatAgeInDays(secret.metadata.creationTimestamp),
    };
  });
  return (
    <>
    <Box className="p-2">
  <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center mb-4">
    <NamespaceFilter />
    <SearchInput value={searchTerm} onChange={setSearchTerm} />
  </div>
      {error ? (
        <Typography color="error">{error}</Typography>
      ) : (
        <DataTable
          rows={rows}
          columns={columns}
          loading={loading}
        />
      )}
      </Box>
    </>
  );
};

export default Secrets;