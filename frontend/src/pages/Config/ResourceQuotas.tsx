import React, { useState, useMemo } from 'react';
import DataTable from '../../components/DataTable';
import { GridColDef } from '@mui/x-data-grid';
import { useResourceQuotas } from '../../hooks/Config/useResourceQuotas';
import { Typography, Box } from '@mui/material';
import { formatAgeInDays } from '../../utils/formDate';
import { useNamespace } from '../../contexts/NamespaceContext';
import NamespaceFilter from '../../components/NamespaceFilter';
import SearchInput from '../../components/SearchInput';

const ResourceQuotas: React.FC = () => {
  const { selectedNamespaces } = useNamespace();
  const [searchTerm, setSearchTerm] = useState('');

  const namespacesToFetch = selectedNamespaces.includes('all') ? ['default'] : selectedNamespaces;
const { resourceQuotas, loading, error } = useResourceQuotas(namespacesToFetch);


  const columns: GridColDef[] = [
    { field: 'id', headerName: 'ID', width: 100 },
    { field: 'name', headerName: 'Name', width: 500 },
    { field: 'namespace', headerName: 'Namespace', width: 200 },
    { field: 'age', headerName: 'Age', width: 120 },
  ];

  const filteredResourceQuotas = useMemo(() => {
    if (!resourceQuotas) return [];

    let result = selectedNamespaces.includes('all')
      ? resourceQuotas
      : resourceQuotas.filter(resource =>
          selectedNamespaces.includes(resource.metadata.namespace)
        );

    if (searchTerm.trim() !== '') {
      const lowerSearch = searchTerm.toLowerCase();
      result = result.filter(resource => {
        const searchable = {
          name: resource.metadata.name,
          namespace: resource.metadata.namespace,
          age: formatAgeInDays(resource.metadata.creationTimestamp),
        };

        return Object.values(searchable).some(value =>
          value.toString().toLowerCase().includes(lowerSearch)
        );
      });
    }

    return result;
  }, [resourceQuotas, selectedNamespaces, searchTerm]);

  const rows = filteredResourceQuotas.map((resource, index) => ({
    id: index + 1,
    name: resource.metadata.name,
    namespace: resource.metadata.namespace || 'default',
    age: formatAgeInDays(resource.metadata.creationTimestamp),
  }));

  return (
    <Box className="p-2">
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center mb-4">
        <NamespaceFilter />
        <SearchInput value={searchTerm} onChange={setSearchTerm} />
      </div>
      {error ? (
        <Typography color="error">{error}</Typography>
      ) : (
        <DataTable rows={rows} columns={columns} loading={loading} />
      )}
    </Box>
  );
};

export default ResourceQuotas;
