import React, {useState, useMemo} from 'react';
import DataTable from '../../components/DataTable';
import { GridColDef } from '@mui/x-data-grid';
import { useHorizontalPods } from '../../hooks/Config/useHorizontalPod';
import { Typography,Box } from '@mui/material';
import { formatAgeInDays } from '../../utils/formDate';
import { useNamespace } from '../../contexts/NamespaceContext';
import NamespaceFilter from '../../components/NamespaceFilter';
import SearchInput from '../../components/SearchInput';

const HorizontalPod: React.FC = () => {
  const { horizontalPod, loading, error } = useHorizontalPods();
  const { selectedNamespaces} = useNamespace();
  const [searchTerm, setSearchTerm] = useState('');
 
  const columns: GridColDef[] = [
    { field: 'id', headerName: 'ID', width: 70 },
    { field: 'name', headerName: 'Name', width: 300 },
    { field: 'namespace', headerName: 'Namespace', width: 150 },
    { field: 'metrics', headerName: 'Metrics', width: 130 },
    { field: 'minPods', headerName: 'Min Pods', width: 100 },
    { field: 'maxPods', headerName: 'Max Pods', width: 100 },
    { field: 'replicas', headerName: 'Replicas', width: 130 },
    { field: 'age', headerName: 'Age', width: 120 },
    { field: 'status', headerName: 'Status', width: 130 },
  ];
  

  const filteredHorizontalPods = useMemo(() => {
              if (!horizontalPod) return [];
            
              let result = selectedNamespaces.includes('all')
                ? horizontalPod
                : horizontalPod.filter(horizontal => selectedNamespaces.includes(horizontal.metadata.namespace));
            
              if (searchTerm.trim() !== '') {
                const lowerSearch = searchTerm.toLowerCase();
                result = result.filter(horizontal => {
                  const horizontalPod = {
                    name: horizontal.metadata.name,
                    namespace: horizontal.metadata.namespace,
                    metrics: horizontal.status.currentCPUUtilizationPercentage !== undefined
                      ? `${horizontal.status.currentCPUUtilizationPercentage}%`
                      : 'N/A',
                    minPods: horizontal.spec.minReplicas ?? 'N/A',
                    maxPods: horizontal.spec.maxReplicas,
                    replicas: `${horizontal.status.currentReplicas} / ${horizontal.status.desiredReplicas}`,
                    status:
                      horizontal.status.currentReplicas === horizontal.status.desiredReplicas
                        ? 'Stable'
                        : 'Scaling',
                    age: formatAgeInDays(horizontal.metadata.creationTimestamp),
                  };
            
                  return Object.values(horizontalPod).some(value => {
                    if (typeof value === 'string' || typeof value === 'number') {
                      return value.toString().toLowerCase().includes(lowerSearch);
                    }
                    return false;
                  });
                });
              }
            
              return result;
            }, [horizontalPod, selectedNamespaces, searchTerm]);
            const rows = (filteredHorizontalPods ?? []).map((horizontal, index) => {
                const status = horizontal.status;
                const spec = horizontal.spec;
              
                return {
                  id: index + 1,
                  name: horizontal.metadata.name,
                  namespace: horizontal.metadata.namespace || 'default',
                  age: formatAgeInDays(horizontal.metadata.creationTimestamp),
                  metrics: status.currentCPUUtilizationPercentage !== undefined
                    ? `${status.currentCPUUtilizationPercentage}%`
                    : 'N/A',
                  minPods: spec.minReplicas ?? 'N/A',
                  maxPods: spec.maxReplicas,
                  replicas: `${status.currentReplicas} / ${status.desiredReplicas}`,
                  status:
                    status.currentReplicas === status.desiredReplicas
                      ? 'Stable'
                      : 'Scaling',
                };
              });
              
  return (
    <>
    <Box className="p-2">
  <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center mb-4">
    <NamespaceFilter />
    <SearchInput value={searchTerm} onChange={setSearchTerm} />
  </div>
      {error ? (
        <Typography color="error">{error}</Typography>
      ) : (
        <DataTable
          rows={rows}
          columns={columns}
          loading={loading}
        />
      )}
      </Box>
    </>
  );
};

export default HorizontalPod;