import React, {useState, useMemo} from 'react';
import DataTable from '../../components/DataTable';
import { GridColDef } from '@mui/x-data-grid';
import { useDaemonSets } from '../../hooks/Workloads/useDaemonSets';
import { Typography,Box} from '@mui/material';
import { formatAgeInDays } from '../../utils/formDate';
import { useNamespace } from '../../contexts/NamespaceContext';
import NamespaceFilter from '../../components/NamespaceFilter';
import SearchInput from '../../components/SearchInput';


const DaemonSets: React.FC = () => {
  const { daemonSets, loading, error } = useDaemonSets();
  const { selectedNamespaces } = useNamespace();
  const [searchTerm, setSearchTerm] = useState('');
 
  const columns: GridColDef[] = [
    { field: 'id', headerName: 'ID', width: 100 },
    { field: 'name', headerName: 'Name', width: 200 },
    { field: 'namespace', headerName: 'Namespace', width: 120 },
    { field: 'desired', headerName: 'Desired', width: 120 },
    { field: 'current', headerName: 'Current', width: 120 },
    { field: 'ready', headerName: 'Ready', width: 120 },
    { field: 'uptodate', headerName: 'Up-To-Date', width: 120 },
    { field: 'available', headerName: 'Available', width: 120 },
    { field: 'nodeSelector', headerName: 'Node Selector', width: 200 },
    { field: 'age', headerName: 'Age', width: 150 },
  ];
  const filteredDaemonSets = useMemo(() => {
        if (!daemonSets) return [];
      
        let result = selectedNamespaces.includes('all')
          ? daemonSets
          : daemonSets.filter(daemon => selectedNamespaces.includes(daemon.metadata.namespace));
      
        if (searchTerm.trim() !== '') {
          const lowerSearch = searchTerm.toLowerCase();
          result = result.filter(dep => {
            const daemonData = {
              name: dep.metadata.name,
              namespace: dep.metadata.namespace,
              desired: dep.status?.desiredNumberScheduled || 0,
              current: dep.status?.currentNumberScheduled || 0,
              ready: dep.status?.numberReady || 0,
              uptodate: dep.status?.updatedNumberScheduled || 0,
              available: dep.status?.numberAvailable || 0,
              nodeSelector: dep.spec?.template.spec.nodeSelector
                ? Object.entries(dep.spec.template.spec.nodeSelector)
                    .map(([key, value]) => `${key}: ${value}`)
                    .join(', ')
                : 'None',
              age: formatAgeInDays(dep.metadata.creationTimestamp),
            };
      
            // Now search across ALL values
            return Object.values(daemonData).some(value => {
              if (typeof value === 'string' || typeof value === 'number') {
                return value.toString().toLowerCase().includes(lowerSearch);
              }
              return false;
            });
          });
        }
      
        return result;
      }, [daemonSets, selectedNamespaces, searchTerm]);

  const rows = (filteredDaemonSets ?? []).map((daemon, index) => {
    
    return {
      id: index + 1,
      name: daemon.metadata.name,
      namespace: daemon.metadata.namespace || 'default',
      desired: daemon.status?.desiredNumberScheduled || 0,
      current: daemon.status?.currentNumberScheduled || 0,
      ready: daemon.status?.numberReady || 0,
      uptodate: daemon.status?.updatedNumberScheduled || 0,
      available: daemon.status?.numberAvailable || 0,
      nodeSelector: daemon.spec?.template.spec.nodeSelector
        ? Object.entries(daemon.spec.template.spec.nodeSelector)
            .map(([key, value]) => `${key}: ${value}`)
            .join(', ')
        : 'None',
      age: formatAgeInDays(daemon.metadata.creationTimestamp),
    };
  });

  return (
    <>
    <Box className="p-2">
  <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center mb-4">
    <NamespaceFilter />
    <SearchInput value={searchTerm} onChange={setSearchTerm} />
  </div>
     {error ? (
  <Typography color="error">{error}</Typography>
) : (
  <DataTable
    rows={rows}
    columns={columns}
    loading={loading}
  />
)}
</Box>
    </>
  );
};

export default DaemonSets;
