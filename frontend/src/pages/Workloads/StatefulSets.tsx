import React, {useState,useMemo} from 'react';
import DataTable from '../../components/DataTable';
import { GridColDef } from '@mui/x-data-grid';
import { useStatefulSets } from '../../hooks/Workloads/useStatefulSets';
import { Typography, Box} from '@mui/material';
import { formatAgeInDays } from '../../utils/formDate';
import { useNamespace } from '../../contexts/NamespaceContext';
import NamespaceFilter from '../../components/NamespaceFilter';
import SearchInput from '../../components/SearchInput';

const StatefulSets: React.FC = () => {
  const { statefulSets, loading, error } = useStatefulSets();
  const { selectedNamespaces} = useNamespace();
  const [searchTerm, setSearchTerm] = useState('');
 
  const columns: GridColDef[] = [
    { field: 'id', headerName: 'ID', width: 100 },
    { field: 'name', headerName: 'Name', width: 600 },
    { field: 'namespace', headerName: 'Namespace', width: 200 },
    { field: 'pods', headerName: 'Pods', width: 120 },
    { field: 'replicas', headerName: 'Replicas', width: 120 },
    { field: 'age', headerName: 'Age', width: 150 },
  ];

  const filteredStatefulSets = useMemo(() => {
          if (!statefulSets) return [];
        
          let result = selectedNamespaces.includes('all')
            ? statefulSets
            : statefulSets.filter(statefulSet => selectedNamespaces.includes(statefulSet.metadata.namespace));
        
          if (searchTerm.trim() !== '') {
            const lowerSearch = searchTerm.toLowerCase();
            result = result.filter(dep => {
              const statefulSet = {
                name: dep.metadata.name,
                namespace: dep.metadata.namespace,
                pods: `${dep.status?.availableReplicas ?? 0}/${dep.status?.replicas ?? 0}`,
                replicas: dep.status?.replicas || 0,
                age: formatAgeInDays(dep.metadata.creationTimestamp),
              };
        
              return Object.values(statefulSet).some(value => {
                if (typeof value === 'string' || typeof value === 'number') {
                  return value.toString().toLowerCase().includes(lowerSearch);
                }
                return false;
              });
            });
          }
        
          return result;
        }, [statefulSets, selectedNamespaces, searchTerm]);
  const rows = (filteredStatefulSets ?? []).map((statefulSet, index) => {
    const replicas = statefulSet.status?.replicas || 0;  
    return {
      id: index + 1,
      name: statefulSet.metadata.name,
      namespace: statefulSet.metadata.namespace || 'default',
      pods: `${statefulSet.status?.availableReplicas ?? 0}/${statefulSet.status?.replicas ?? 0}`,
      replicas: replicas,
      age: formatAgeInDays(statefulSet.metadata.creationTimestamp),
    };
  });

  return (
    <>
    <Box className="p-2">
  <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center mb-4">
    <NamespaceFilter />
    <SearchInput value={searchTerm} onChange={setSearchTerm} />
  </div>
     {error ? (
  <Typography color="error">{error}</Typography>
) : (
  <DataTable
    rows={rows}
    columns={columns}
    loading={loading}
  />
)}
</Box>
    </>
  );
};

export default StatefulSets;
