import React, {useState, useMemo} from 'react';
import DataTable from '../../components/DataTable';
import { GridColDef } from '@mui/x-data-grid';
import { useJobs } from '../../hooks/Workloads/useJobs'; 
import { Typography,Chip,Box } from '@mui/material';
import { formatAgeInDays } from '../../utils/formDate';
import { useNamespace } from '../../contexts/NamespaceContext';
import NamespaceFilter from '../../components/NamespaceFilter';
import SearchInput from '../../components/SearchInput';

const Jobs: React.FC = () => {
  const { jobs, loading, error } = useJobs();
  const { selectedNamespaces} = useNamespace();
  const [searchTerm, setSearchTerm] = useState('');
 
  const columns: GridColDef[] = [
    { field: 'id', headerName: 'ID', width: 100 },
    { field: 'name', headerName: 'Name', width: 500 },
    { field: 'namespace', headerName: 'Namespace', width: 200 },
    { field: 'completion', headerName: 'Completion', width: 200 },
    { field: 'age', headerName: 'Age', width: 120 },
    {
      field: 'condition',
      headerName: 'Condition',
      width: 150,
      renderCell: (params) => {
        const condition = params.value;
        let color: 'default' | 'primary' | 'secondary' | 'error' | 'success' | 'warning' = 'default';
  
        switch (condition) {
          case 'Completed':
            color = 'success';
            break;
          case 'Failed':
            color = 'error';
            break;
          case 'Running':
            color = 'primary';
            break;
          case 'Ready':
            color = 'secondary';
            break;
          case 'Status Pending':
          default:
            color = 'warning';
            break;
        }
  
        return <Chip label={condition} color={color} size="small" />;
      },
    },
  ];

  const filteredJobs = useMemo(() => {
              if (!jobs) return [];
            
              let result = selectedNamespaces.includes('all')
                ? jobs
                : jobs.filter(job => selectedNamespaces.includes(job.metadata.namespace));
            
              if (searchTerm.trim() !== '') {
                const lowerSearch = searchTerm.toLowerCase();
                result = result.filter(dep => {
                  const statefulSet = {
                    name: dep.metadata.name,
                    namespace: dep.metadata.namespace,
                    completion: dep.status?.completionTime
                      ? new Date(dep.status.completionTime).toLocaleString()
                      : 'Not completed',
                    age: formatAgeInDays(dep.metadata.creationTimestamp),
                    condition: dep.status?.conditions
                      ? dep.status.conditions
                          .map(c => c.type)
                          .join(', ')
                      : 'Status Pending',
                    
                  };
            
                  return Object.values(statefulSet).some(value => {
                    if (typeof value === 'string' || typeof value === 'number') {
                      return value.toString().toLowerCase().includes(lowerSearch);
                    }
                    return false;
                  });
                });
              }
            
              return result;
            }, [jobs, selectedNamespaces, searchTerm]);
  const rows = (filteredJobs ?? []).map((job, index) => {
    // Format completion time if it exists
    const completionTime = job.status?.completionTime 
      ? new Date(job.status.completionTime).toLocaleString() 
      : 'Not completed';

    // Get the most relevant condition message
    let condition = 'Status Pending';
    if (job.status?.conditions) {
      const completeCondition = job.status.conditions.find(c => c.type === 'Complete');
      const failedCondition = job.status.conditions.find(c => c.type === 'Failed');
      
      if (completeCondition && completeCondition.status === 'True') {
        condition = 'Completed';
      } else if (failedCondition && failedCondition.status === 'True') {
        condition = 'Failed';
      } else if ((job.status?.active ?? 0) > 0) {
        condition = 'Running';
      } else {
        condition = 'Pending';
      }
    }

    return {
      id: index + 1,
      name: job.metadata.name,
      namespace: job.metadata.namespace || 'default',
      completion: completionTime,
      age: formatAgeInDays(job.metadata.creationTimestamp),
      condition: condition,
    };
  });

  return (
    <>
    <Box className="p-2">
  <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center mb-4">
    <NamespaceFilter />
    <SearchInput value={searchTerm} onChange={setSearchTerm} />
  </div>
      {error ? (
        <Typography color="error">{error}</Typography>
      ) : (
        <DataTable
          rows={rows}
          columns={columns}
          loading={loading}
        />
      )}
      </Box>
    </>
  );
};

export default Jobs;