import React, { useState, useMemo } from 'react';
import DataTable from '../../components/DataTable';
import { GridColDef } from '@mui/x-data-grid';
import { usePods } from '../../hooks/Workloads/usePods';
import { LinearProgress, Box, Drawer, Typography, Divider, Tooltip } from '@mui/material';
import { formatAgeInDays } from '../../utils/formDate';
import { useNamespace } from '../../contexts/NamespaceContext';
import NamespaceFilter from '../../components/NamespaceFilter';
import SearchInput from '../../components/SearchInput';

const PodsList: React.FC = () => {
  const { pods, loading, error } = usePods();
  const { selectedNamespaces } = useNamespace();
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [selectedPod, setSelectedPod] = useState<any>(null);
  const [searchTerm, setSearchTerm] = useState('');

  const handleRowClick = (params: any) => {
    setSelectedPod(params.row);
    setDrawerOpen(true);
  };

  const columns: GridColDef[] = [
    { field: 'id', headerName: 'ID', width: 100 },
    { field: 'name', headerName: 'Name', width: 200 },
    { field: 'namespace', headerName: 'Namespace', width: 120 },
    {
      field: 'cpu',
      headerName: 'CPU',
      width: 200,
      renderCell: () => (
        <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', height: '100%', padding: 1 }}>
          <LinearProgress variant="determinate" value={0} sx={{ width: '100%', height: 10, borderRadius: 5 }} />
        </Box>
      ),
    },
    {
      field: 'memory',
      headerName: 'Memory',
      width: 200,
      renderCell: () => (
        <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', height: '100%', padding: 1 }}>
          <LinearProgress variant="determinate" value={0} sx={{ width: '100%', height: 10, borderRadius: 5 }} />
        </Box>
      ),
    },
    {
      field: 'restartCount',
      headerName: 'Restarts',
      width: 80,
    },
    {
      field: 'containers',
      headerName: 'Containers',
      width: 100,
      renderCell: (params) => {
        const { init = [], main = [] } = params.value || {};
        return (
          <Box sx={{ display: 'flex', gap: 0.5, alignItems: 'center', width: '100%', height: '100%', padding: 1 }}>
            {main.map((container: any, idx: number) => (
              <Tooltip
                key={`main-${idx}`}
                title={
                  <Box sx={{ fontSize: 12 }}>
                    <div><strong>{container.name}</strong> {container.ready ? '✅ Ready' : ''}</div>
                    <div><strong>Started:</strong> {container.state?.running?.startedAt || 'N/A'}</div>
                    <div><strong>Container ID:</strong> {container.containerID || 'N/A'}</div>
                  </Box>
                }
              >
                <Box sx={{ width: 10, height: 10, borderRadius: '2px', backgroundColor: '#4caf50' }} />
              </Tooltip>
            ))}
            {init.map((container: any, idx: number) => (
              <Tooltip
                key={`init-${idx}`}
                title={
                  container.state?.terminated
                    ? (
                      <>
                        <div><strong>Exit Code:</strong> {container.state.terminated.exitCode}</div>
                        <div><strong>Reason:</strong> {container.state.terminated.reason}</div>
                        <div><strong>Started:</strong> {container.state.terminated.startedAt}</div>
                        <div><strong>Finished:</strong> {container.state.terminated.finishedAt}</div>
                      </>
                    )
                    : 'No termination info'
                }
              >
                <Box sx={{ width: 10, height: 10, borderRadius: '2px', backgroundColor: '#9e9e9e' }} />
              </Tooltip>
            ))}
          </Box>
        );
      },
    },
    { field: 'controlledBy', headerName: 'Controlled By', width: 130 },
    { field: 'nodename', headerName: 'Node name', width: 130 },
    { field: 'qos', headerName: 'QoS', width: 100 },
    { field: 'age', headerName: 'Age', width: 80 },
    {
      field: 'status',
      headerName: 'Status',
      width: 120,
      renderCell: (params) => {
        const isRunning = params.value === 'Running';
        return (
          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', height: '100%', gap: 1 }}>
            <Box sx={{
              width: 10,
              height: 10,
              borderRadius: '50%',
              backgroundColor: isRunning ? '#4caf50' : '#f44336',
            }} />
            <Typography variant="body2" sx={{ color: isRunning ? '#4caf50' : '#f44336' }}>
              {params.value}
            </Typography>
          </Box>
        );
      },
    },
  ];

  const filteredPods = useMemo(() => {
    if (!pods) return [];
  
    let result = selectedNamespaces.includes('all')
      ? pods
      : pods.filter(pod => selectedNamespaces.includes(pod.metadata.namespace));
  
    if (searchTerm.trim() !== '') {
      const lowerSearch = searchTerm.toLowerCase();
      result = result.filter(pod => {
        const podData = {
          name: pod.metadata.name,
          namespace: pod.metadata.namespace,
          age: formatAgeInDays(pod.metadata.creationTimestamp),
          cpu: 0, // You might calculate real CPU/memory usage if needed
          memory: 0,
          restartCount: pod.status?.containerStatuses?.reduce((acc: number, container: any) => acc + container.restartCount, 0) || 0,
          containers: {
            init: pod.status?.initContainerStatuses ?? [],
            main: pod.status?.containerStatuses ?? [],
          },
          controlledBy: pod.metadata.ownerReferences?.[0]?.kind || 'N/A',
          nodename: pod.spec?.nodeName || 'N/A',
          qos: pod.status?.qosClass || 'N/A',
          status: pod.status?.phase || 'N/A',
        };
  
        // Now search across ALL values
        return Object.values(podData).some(value => {
          if (typeof value === 'string' || typeof value === 'number') {
            return value.toString().toLowerCase().includes(lowerSearch);
          }
          return false;
        });
      });
    }
  
    return result;
  }, [pods, selectedNamespaces, searchTerm]);
  

  const rows = (filteredPods ?? []).map((pod, index) => ({
    id: index + 1,
    name: pod.metadata.name,
    namespace: pod.metadata.namespace,
    age: formatAgeInDays(pod.metadata.creationTimestamp),
    cpu: 0,
    memory: 0,
    restartCount: pod.status?.containerStatuses?.reduce((acc: number, container: any) => acc + container.restartCount, 0) || 0,
    containers: {
      init: pod.status?.initContainerStatuses ?? [],
      main: pod.status?.containerStatuses ?? [],
    },
    controlledBy: pod.metadata.ownerReferences?.[0]?.kind || 'N/A',
    nodename: pod.spec?.nodeName || 'N/A',
    qos: pod.status?.qosClass || 'N/A',
    status: pod.status?.phase || 'N/A',
  }));

  return (
    <Box className="p-2">
  <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center mb-4">
    <NamespaceFilter />
    <SearchInput value={searchTerm} onChange={setSearchTerm} />
  </div>

      {error ? (
        <Typography color="error">{error}</Typography>
      ) : (
        <DataTable
          rows={rows}
          columns={columns}
          loading={loading}
          onRowClick={handleRowClick}
        />
      )}

      <Drawer
        anchor="right"
        open={drawerOpen}
        onClose={() => setDrawerOpen(false)}
        PaperProps={{ sx: { width: 400, p: 2 } }}
      >
        {selectedPod && (
          <Box>
            <Typography variant="h6" gutterBottom>Node Details</Typography>
            <Divider sx={{ mb: 2 }} />
            <Typography><strong>Name:</strong> {selectedPod.name}</Typography>
            <Typography><strong>Status:</strong> {selectedPod.status}</Typography>
            <Typography><strong>Age:</strong> {selectedPod.age}</Typography>
            <Typography><strong>Node:</strong> {selectedPod.nodename}</Typography>
            <Typography><strong>QoS:</strong> {selectedPod.qos}</Typography>
          </Box>
        )}
      </Drawer>
    </Box>
  );
};

export default PodsList;
