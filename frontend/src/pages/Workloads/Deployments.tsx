import React, {useState, useMemo } from 'react';
import DataTable from '../../components/DataTable';
import { GridColDef } from '@mui/x-data-grid';
import { useDeployments } from '../../hooks/Workloads/useDeployments';
import { Typography,Box} from '@mui/material';
import { formatAgeInDays } from '../../utils/formDate';
import { useNamespace } from '../../contexts/NamespaceContext';
import NamespaceFilter from '../../components/NamespaceFilter';
import SearchInput from '../../components/SearchInput';

const Deployments: React.FC = () => {
  const { deployments, loading, error } = useDeployments();
  const { selectedNamespaces} = useNamespace();
  const [searchTerm, setSearchTerm] = useState('');
 
  const columns: GridColDef[] = [
    { field: 'id', headerName: 'ID', width: 100 },
    { field: 'name', headerName: 'Name', width: 200 },
    { field: 'namespace', headerName: 'Namespace', width: 120 },
    { field: 'pods', headerName: 'Pods', width: 120 },
    { field: 'replicas', headerName: 'Replicas', width: 120 },
    { field: 'age', headerName: 'Age', width: 150 },
    {
        field: 'status',
        headerName: 'Status',
        width: 120,
        renderCell: (params) => {
          const status = params.value;
          const isRunning = status === 'Running';
          return (
            <Box sx={{ display: 'flex', alignItems: 'center', width:'100%', height:'100%', gap: 1 }}>
              <Box
                sx={{
                  width: 10,
                  height: 10,
                  borderRadius: '50%',
                  backgroundColor: isRunning ? '#4caf50' : '#f44336', // green or red
                }}
              />
              <Typography variant="body2" sx={{ color: isRunning ? '#4caf50' : '#f44336' }}>
                {status}
              </Typography>
            </Box>
          );
        },
      },
  ];

  const filteredDeployment = useMemo(() => {
      if (!deployments) return [];
    
      let result = selectedNamespaces.includes('all')
        ? deployments
        : deployments.filter(dep => selectedNamespaces.includes(dep.metadata.namespace));
    
      if (searchTerm.trim() !== '') {
        const lowerSearch = searchTerm.toLowerCase();
        result = result.filter(dep => {
          const podData = {
            name: dep.metadata.name,
            namespace: dep.metadata.namespace,
            age: formatAgeInDays(dep.metadata.creationTimestamp),
            pods: `${dep.status?.availableReplicas ?? 0}/${dep.status?.replicas ?? 0}`,
            replicas: dep.status?.replicas || 0,
            status: (dep.status?.replicas ?? 0) > 0 ? 'Running' : 'Stopped',
          };
    
          // Now search across ALL values
          return Object.values(podData).some(value => {
            if (typeof value === 'string' || typeof value === 'number') {
              return value.toString().toLowerCase().includes(lowerSearch);
            }
            return false;
          });
        });
      }
    
      return result;
    }, [deployments, selectedNamespaces, searchTerm]);

  const rows = (filteredDeployment ?? []).map((deployment, index) => {
  const replicas = deployment.status?.replicas || 0;  
    return {
      id: index + 1,
      name: deployment.metadata.name,
      namespace: deployment.metadata.namespace || 'default',
      pods: `${deployment.status?.availableReplicas ?? 0}/${deployment.status?.replicas ?? 0}`,
      replicas: replicas,
      age: formatAgeInDays(deployment.metadata.creationTimestamp),
      status: replicas > 0 ? 'Running' : 'Stopped',
    };
  });

  return (
    <>
    <Box className="p-2">
  <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center mb-4">
    <NamespaceFilter />
    <SearchInput value={searchTerm} onChange={setSearchTerm} />
  </div>
     {error ? (
  <Typography color="error">{error}</Typography>
) : (
  <DataTable
    rows={rows}
    columns={columns}
    loading={loading}
  />
)}
    </Box>
    </>
  );
};

export default Deployments;
