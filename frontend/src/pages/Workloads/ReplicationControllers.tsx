import React, {useState, useMemo} from 'react';
import DataTable from '../../components/DataTable';
import { GridColDef } from '@mui/x-data-grid';
import { useReplicationControllers } from '../../hooks/Workloads/useReplicationControllers';
import { Typography, Box} from '@mui/material';
import { useNamespace } from '../../contexts/NamespaceContext';
import NamespaceFilter from '../../components/NamespaceFilter';
import SearchInput from '../../components/SearchInput';

const ReplicationControllers: React.FC = () => {
  const { replicationCont, loading, error } = useReplicationControllers();
  const { selectedNamespaces} = useNamespace();
  const [searchTerm, setSearchTerm] = useState('');
 
  const columns: GridColDef[] = [
    { field: 'id', headerName: 'ID', width: 100 },
    { field: 'name', headerName: 'Name', width: 500 },
    { field: 'namespace', headerName: 'Namespace', width: 120 },
    { field: 'replicas', headerName: 'Replicas', width: 120 },
    { field: 'desiredReplicas', headerName: 'Desired Replicas', width: 170 },
    { field: 'selector', headerName: 'Selector', width: 120 },
  ];

  const filteredReplicaCont = useMemo(() => {
            if (!replicationCont) return [];
          
            let result = selectedNamespaces.includes('all')
              ? replicationCont
              : replicationCont.filter(replicationCont => selectedNamespaces.includes(replicationCont.metadata.namespace));
          
            if (searchTerm.trim() !== '') {
              const lowerSearch = searchTerm.toLowerCase();
              result = result.filter(dep => {
                const replica = {
                  name: dep.metadata.name,
                  namespace: dep.metadata.namespace,
                  replicas: dep.status?.replicas || 0,
                  selector: dep.spec?.selector
                    ? Object.entries(dep.spec?.selector)
                        .map(([key, value]) => `${key}=${value}`)
                        .join(', ')
                    : 'none',
                  desiredReplicas: dep.spec?.replicas || 0,
                };
          
                return Object.values(replica).some(value => {
                  if (typeof value === 'string' || typeof value === 'number') {
                    return value.toString().toLowerCase().includes(lowerSearch);
                  }
                  return false;
                });
              });
            }
          
            return result;
          }, [replicationCont, selectedNamespaces, searchTerm]);
  const rows = (filteredReplicaCont?? []).map((replicaCont, index) => {
    // Format selector object into string (e.g., "app=my-app,tier=frontend")
    const selectorString = replicaCont.spec?.selector 
        ? Object.entries(replicaCont.spec.selector)
            .map(([key, value]) => `${key}=${value}`)
            .join(', ')
        : 'none';

    return {
        id: index + 1,
        name: replicaCont.metadata.name,
        namespace: replicaCont.metadata.namespace || 'default',
        replicas: replicaCont.status?.replicas || 0,
        desiredReplicas: replicaCont.spec?.replicas || 0,
        selector: selectorString,  
    };
});

  return (
    <>
    <Box className="p-2">
  <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center mb-4">
    <NamespaceFilter />
    <SearchInput value={searchTerm} onChange={setSearchTerm} />
  </div>
     {error ? (
  <Typography color="error">{error}</Typography>
) : (
  <DataTable
    rows={rows}
    columns={columns}
    loading={loading}
  />
)}
</Box>
    </>
  );
};

export default ReplicationControllers;
