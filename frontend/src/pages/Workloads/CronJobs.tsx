import React, { useState, useMemo, useEffect } from 'react';
import DataTable from '../../components/DataTable';
import { GridColDef } from '@mui/x-data-grid';
import { useCronJobs } from '../../hooks/Workloads/useCronJobs';
import { Typography, Box } from '@mui/material';
import { formatAgeInDays } from '../../utils/formDate';
import { useNamespace } from '../../contexts/NamespaceContext';
import NamespaceFilter from '../../components/NamespaceFilter';
import SearchInput from '../../components/SearchInput';
import { getNextExecutionTime } from '../../utils/cronUtils';

const CronJobs: React.FC = () => {
  const { cronJobs, loading, error } = useCronJobs();
  const { selectedNamespaces } = useNamespace();
  const [searchTerm, setSearchTerm] = useState('');
  const [now, setNow] = useState(new Date());

  // Live update every second
  useEffect(() => {
    const interval = setInterval(() => {
      setNow(new Date());
    }, 1000);
    return () => clearInterval(interval);
  }, []);

  // Shared formatter
  const formatDuration = (ms: number): string => {
    if (ms <= 0) return '00:00:00';
    const hours = Math.floor(ms / (1000 * 60 * 60));
    const minutes = Math.floor((ms % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((ms % (1000 * 60)) / 1000);
    const pad = (n: number) => n.toString().padStart(2, '0');
    return `${pad(hours)}:${pad(minutes)}:${pad(seconds)}`;
  };

  const columns: GridColDef[] = [
    { field: 'id', headerName: 'ID', width: 100 },
    { field: 'name', headerName: 'Name', width: 200 },
    { field: 'namespace', headerName: 'Namespace', width: 150 },
    { field: 'schedule', headerName: 'Schedule', width: 120 },
    { field: 'active', headerName: 'Active', width: 120 },
    { field: 'lastSchedule', headerName: 'Last schedule', width: 250 },
    { field: 'nextExecution', headerName: 'Next execution', width: 250 },
    { field: 'timeZone', headerName: 'Time Zone', width: 120 },
    { field: 'age', headerName: 'Age', width: 150 },
  ];

  const filteredCronJobs = useMemo(() => {
    if (!cronJobs) return [];

    let result = selectedNamespaces.includes('all')
      ? cronJobs
      : cronJobs.filter(cron => selectedNamespaces.includes(cron.metadata.namespace));

    if (searchTerm.trim() !== '') {
      const lowerSearch = searchTerm.toLowerCase();
      result = result.filter(dep => {
        const cronJob = {
          name: dep.metadata.name,
          namespace: dep.metadata.namespace,
          schedule: dep.spec.schedule,
          active: dep.status?.active?.length || 0,
          lastSchedule: dep.status?.lastScheduleTime
            ? new Date(dep.status.lastScheduleTime).toLocaleString()
            : 'Never',
          nextExecution: dep.status?.nextScheduleTime
            ? new Date(dep.status.nextScheduleTime).toLocaleString()
            : 'Unknown',
          timeZone: dep.spec.timeZone || 'UTC',
          age: formatAgeInDays(dep.metadata.creationTimestamp),
        };

        return Object.values(cronJob).some(value => {
          if (typeof value === 'string' || typeof value === 'number') {
            return value.toString().toLowerCase().includes(lowerSearch);
          }
          return false;
        });
      });
    }

    return result;
  }, [cronJobs, selectedNamespaces, searchTerm]);

  const rows = useMemo(() => {
    return (filteredCronJobs ?? []).map((cronJob, index) => {
      const activeJobs = cronJob.status?.active?.length || 0;

      // LAST SCHEDULE (count up)
      const lastScheduleDate = cronJob.status?.lastScheduleTime
        ? new Date(cronJob.status.lastScheduleTime)
        : null;

      const lastScheduleFormatted = lastScheduleDate
        ? `${lastScheduleDate.toLocaleString()} (${formatDuration(now.getTime() - lastScheduleDate.getTime())})`
        : 'Never';

      // NEXT EXECUTION (countdown)
      const nextExecution = getNextExecutionTime(cronJob.spec.schedule);
      const nextExecutionFormatted = nextExecution
        ? `${nextExecution.toLocaleString()} (${formatDuration(nextExecution.getTime() - now.getTime())})`
        : 'Invalid schedule';

      return {
        id: index + 1,
        name: cronJob.metadata.name,
        namespace: cronJob.metadata.namespace || 'default',
        schedule: cronJob.spec.schedule,
        active: activeJobs,
        lastSchedule: lastScheduleFormatted,
        nextExecution: nextExecutionFormatted,
        timeZone: cronJob.spec.timeZone || 'UTC',
        age: formatAgeInDays(cronJob.metadata.creationTimestamp),
      };
    });
  }, [filteredCronJobs, now]);

  return (
    <Box className="p-2">
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center mb-4">
        <NamespaceFilter />
        <SearchInput value={searchTerm} onChange={setSearchTerm} />
      </div>
      {error ? (
        <Typography color="error">{error}</Typography>
      ) : (
        <DataTable
          rows={rows}
          columns={columns}
          loading={loading}
        />
      )}
    </Box>
  );
};

export default CronJobs;
