import React, { useState, useMemo } from 'react';
import DataTable from '../../components/DataTable';
import { GridColDef } from '@mui/x-data-grid';
import { useReplicaSets } from '../../hooks/Workloads/useReplicaSets';
import { Typography, Box } from '@mui/material';
import { formatAgeInDays } from '../../utils/formDate';
import { useNamespace } from '../../contexts/NamespaceContext';
import NamespaceFilter from '../../components/NamespaceFilter';
import SearchInput from '../../components/SearchInput';

const Replicas: React.FC = () => {
  const { replicas, loading, error } = useReplicaSets();
  const { selectedNamespaces } = useNamespace();
  const [searchTerm, setSearchTerm] = useState('');

  const columns: GridColDef[] = [
    { field: 'id', headerName: 'ID', width: 100 },
    { field: 'name', headerName: 'Name', width: 400 },
    { field: 'namespace', headerName: 'Namespace', width: 120 },
    { field: 'desired', headerName: 'Desired', width: 120 },
    { field: 'current', headerName: 'Current', width: 120 },
    { field: 'ready', headerName: 'Ready', width: 120 },
    { field: 'age', headerName: 'Age', width: 150 },
  ];

  const filteredReplicas = useMemo(() => {
    if (!replicas) return [];

    let result = selectedNamespaces.includes('all')
      ? replicas
      : replicas.filter(rep => selectedNamespaces.includes(rep.metadata.namespace));

    if (searchTerm.trim() !== '') {
      const lowerSearch = searchTerm.toLowerCase();
      result = result.filter(replica => 
        replica.metadata.name.toLowerCase().includes(lowerSearch) ||
        replica.metadata.namespace?.toLowerCase().includes(lowerSearch) ||
        String(replica.spec?.replicas || 0).includes(lowerSearch) ||
        String(replica.status?.replicas || 0).includes(lowerSearch) ||
        String(replica.status?.readyReplicas || 0).includes(lowerSearch) ||
        formatAgeInDays(replica.metadata.creationTimestamp).toString().includes(lowerSearch)
      );
    }

    return result;
  }, [replicas, selectedNamespaces, searchTerm]);

  const rows = (filteredReplicas ?? []).map((replica, index) => ({
    id: index + 1,
    name: replica.metadata.name,
    namespace: replica.metadata.namespace || 'default',
    desired: replica.spec?.replicas || 0,
    current: replica.status?.replicas || 0,
    ready: replica.status?.readyReplicas || 0,
    age: formatAgeInDays(replica.metadata.creationTimestamp),
  }));

  return (
    <Box className="p-2">
  <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center mb-4">
    <NamespaceFilter />
    <SearchInput value={searchTerm} onChange={setSearchTerm} />
  </div>
  {error ? (
    <Typography color="error">{error}</Typography>
  ) : (
    <DataTable
      rows={rows}
      columns={columns}
      loading={loading}
    />
  )}
</Box>

  );
};

export default Replicas;
