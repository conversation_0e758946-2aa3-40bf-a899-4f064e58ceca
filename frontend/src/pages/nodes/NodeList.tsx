import React, { useState, useMemo } from 'react';
import DataTable from '../../components/DataTable';
import { GridColDef } from '@mui/x-data-grid';
import {useNodes} from '../../hooks/nodes/useNodes';
import { LinearProgress, Box, Drawer, Typography, Divider } from '@mui/material';
import { formatAgeInDays } from '../../utils/formDate';
import SearchInput from '../../components/SearchInput';

const NodeList: React.FC = () => {
  const { nodes, loading, error } = useNodes();
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [selectedNode, setSelectedNode] = useState<any>(null);
  const [searchTerm, setSearchTerm] = useState('');

  const handleRowClick = (params: any) => {
    setSelectedNode(params.row);
    setDrawerOpen(true);
  };

  const columns: GridColDef[] = [
    { field: 'id', headerName: 'ID', width: 100 },
    { field: 'name', headerName: 'Name', width: 200 },
    {
      field: 'cpu',
      headerName: 'CPU',
      width: 200,
      renderCell: () => (
        <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', height: '100%', padding: 1 }}>
          <LinearProgress variant="determinate" value={0} sx={{ width: '100%', height: 10, borderRadius: 5 }} />
        </Box>
      ),
    },
    {
      field: 'memory',
      headerName: 'Memory',
      width: 200,
      renderCell: () => (
        <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', height: '100%', padding: 1 }}>
          <LinearProgress variant="determinate" value={0} sx={{ width: '100%', height: 10, borderRadius: 5 }} />
        </Box>
      ),
    },
    {
      field: 'disk',
      headerName: 'Disk',
      width: 200,
      renderCell: () => (
        <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', height: '100%', padding: 1 }}>
          <LinearProgress variant="determinate" value={0} sx={{ width: '100%', height: 10, borderRadius: 5 }} />
        </Box>
      ),
    },
    { field: 'taints', headerName: 'Taint', width: 100 },
    { field: 'roles', headerName: 'Roles', width: 180 },
    { field: 'version', headerName: 'Version', width: 100 },
    { field: 'age', headerName: 'Age', width: 150 },
    {
      field: 'status',
      headerName: 'Status',
      width: 120,
      renderCell: (params) => {
        const isReady = params.value === 'Ready';
        return (
          <span
            style={{
              color: isReady ? '#4caf50' : '#f44336',
              fontWeight: 'bold',
              padding: '4px 8px',
              borderRadius: '4px',
              backgroundColor: isReady ? 'rgba(76, 175, 80, 0.1)' : 'rgba(244, 67, 54, 0.1)',
            }}
          >
            {params.value}
          </span>
        );
      },
    },
  ];

  const filteredNodes = useMemo(() => {
    if (!nodes) return [];
  
    let result = nodes;
  
    if (searchTerm.trim() !== '') {
      const lowerSearch = searchTerm.toLowerCase();
      result = result.filter(node => {
        const nodeName = node.metadata.name.toLowerCase();
        const taints = node.spec?.taints?.length?.toString() || '0';
        const roles = Object.keys(node.metadata.labels || {})
          .filter(key => key.startsWith('node-role.kubernetes.io/'))
          .map(key => key.replace('node-role.kubernetes.io/', ''))
          .join(', ')
          .toLowerCase();
        const version = (node.status?.nodeInfo?.kubeletVersion || 'unknown').toLowerCase();
        const age = formatAgeInDays(node.metadata.creationTimestamp).toString();
  
        return (
          nodeName.includes(lowerSearch) ||
          taints.includes(lowerSearch) ||
          roles.includes(lowerSearch) ||
          version.includes(lowerSearch) ||
          age.includes(lowerSearch)
        );
      });
    }
  
    return result;
  }, [nodes, searchTerm]);
  
  const rows = (filteredNodes ?? []).map((node, index) => {
    
    const roles = Object.keys(node.metadata.labels || {})
      .filter(key => key.startsWith('node-role.kubernetes.io/'))
      .map(key => key.replace('node-role.kubernetes.io/', ''))
      .join(', ');

    const readyCondition = node.status?.conditions?.find(cond => cond.type === 'Ready');
    const status = readyCondition?.status === 'True' ? 'Ready' : 'Not Ready';

    return {
      id: index + 1,
      name: node.metadata.name,
      age: formatAgeInDays(node.metadata.creationTimestamp),
      taints: node.spec?.taints ? node.spec.taints.length : 0,
      roles: roles || 'worker',
      version: node.status?.nodeInfo?.kubeletVersion || 'unknown',
      status: status,
      cpu: 0,
      memory: 0,
      disk: 0,
    };
  });

  return (
    <>
   <Box className="p-2">
  <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center mb-4">
    <SearchInput value={searchTerm} onChange={setSearchTerm} />
  </div>
     {error ? (
  <Typography color="error">{error}</Typography>
) : (
  <DataTable
    rows={rows}
    columns={columns}
    loading={loading}
    onRowClick={handleRowClick}
  />
)}


      <Drawer
        anchor="right"
        open={drawerOpen}
        onClose={() => setDrawerOpen(false)}
        PaperProps={{ sx: { width: 400, p: 2 } }}
      >
        {selectedNode ? (
          <Box>
            <Typography variant="h6" gutterBottom>
              Node Details
            </Typography>
            <Divider sx={{ mb: 2 }} />
            <Typography><strong>Name:</strong> {selectedNode.name}</Typography>
            <Typography><strong>Status:</strong> {selectedNode.status}</Typography>
            <Typography><strong>Roles:</strong> {selectedNode.roles}</Typography>
            <Typography><strong>Version:</strong> {selectedNode.version}</Typography>
            <Typography><strong>Age:</strong> {selectedNode.age}</Typography>
            <Typography><strong>Taints:</strong> {selectedNode.taints}</Typography>
          </Box>
        ) : null}
      </Drawer>
      </Box>
    </>
  );
};

export default NodeList;
