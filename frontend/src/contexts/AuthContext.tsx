import React, { createContext, useState, useContext, useEffect, ReactNode } from 'react';
import axios from 'axios';

// Define URLs from environment variables
const API_URL = import.meta.env.VITE_API_BASE_URL;
const BACKEND_URL = import.meta.env.VITE_BACKEND_URL || 'https://backend.redalerts.tn';

// Define the shape of our auth context
interface AuthContextType {
  isAuthenticated: boolean;
  user: any | null;
  loading: boolean;
  error: string | null;
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
}

// Create the context with a default value
const AuthContext = createContext<AuthContextType>({
  isAuthenticated: false,
  user: null,
  loading: true,
  error: null,
  login: async () => {},
  logout: async () => {},
});

// Custom hook to use the auth context
export const useAuth = () => useContext(AuthContext);

interface AuthProviderProps {
  children: ReactNode;
}

// Provider component that wraps the app and makes auth object available
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [user, setUser] = useState<any | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Configure axios
  axios.defaults.baseURL = API_URL;
  axios.defaults.headers.common['Accept'] = 'application/json';
  axios.defaults.headers.common['Content-Type'] = 'application/json';
  axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';
  axios.defaults.withCredentials = true; // Important for cookies/CSRF

  // Check if user is already authenticated on mount
  useEffect(() => {
    const checkAuthStatus = async () => {
      const token = localStorage.getItem('auth_token');

      if (token) {
        try {
          console.log('AuthContext: Checking auth status with token');
          // Set the auth token in axios headers
          axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

          // Fetch the current user
          const response = await axios.get('/user');

          if (response.data) {
            console.log('AuthContext: User authenticated:', response.data);
            setUser(response.data);
            setIsAuthenticated(true);
          }
        } catch (err: any) {
          console.error('AuthContext: Auth check failed:', err.message);
          console.error('AuthContext: Status:', err.response?.status);
          console.error('AuthContext: Data:', err.response?.data);

          // If the token is invalid, clear it
          localStorage.removeItem('auth_token');
          axios.defaults.headers.common['Authorization'] = '';
        }
      } else {
        console.log('AuthContext: No auth token found');
      }

      setLoading(false);
    };

    checkAuthStatus();
  }, []);

  // Login function
  const login = async (email: string, password: string) => {
    setLoading(true);
    setError(null);

    console.log('AuthContext: Starting login process...');
    console.log('API URL:', API_URL);

    try {
      // Get CSRF cookie first if using cookie-based authentication
      console.log('AuthContext: Getting CSRF cookie...');
      await axios.get(`${BACKEND_URL}/sanctum/csrf-cookie`);

      console.log('AuthContext: Sending login request...');
      const response = await axios.post('/login', { email, password });

      console.log('AuthContext: Login response received:', response.data);

      if (response.data.token) {
        console.log('AuthContext: Token received, storing token...');
        // Store the token
        localStorage.setItem('auth_token', response.data.token);

        // Set the auth token in axios headers
        axios.defaults.headers.common['Authorization'] = `Bearer ${response.data.token}`;

        console.log('AuthContext: Updating state...');
        // Update state
        setUser(response.data.user);
        setIsAuthenticated(true);
        console.log('AuthContext: Authentication state updated to true');
      } else {
        console.error('AuthContext: No token in response');
        throw new Error('Authentication failed - No token received');
      }
    } catch (err: any) {
      console.error('AuthContext: Login error:', err);
      console.error('AuthContext: Response status:', err.response?.status);
      console.error('AuthContext: Response data:', err.response?.data);

      setError(err.response?.data?.message || 'Authentication failed');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Logout function
  const logout = async () => {
    setLoading(true);

    try {
      // Call the logout endpoint
      await axios.post('/logout');
    } catch (err) {
      console.error('Logout error:', err);
    } finally {
      // Clear token and state regardless of API response
      localStorage.removeItem('auth_token');
      localStorage.removeItem('selectedCluster');
      axios.defaults.headers.common['Authorization'] = '';
      setUser(null);
      setIsAuthenticated(false);
      setLoading(false);
    }
  };

  // Provide the auth context value
  const value = {
    isAuthenticated,
    user,
    loading,
    error,
    login,
    logout
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
