import React, { createContext, useState, ReactNode, useContext, useEffect } from 'react';

interface NamespaceContextType {
  selectedNamespaces: string[];
  setSelectedNamespaces: (namespaces: string[]) => void;
}

const NamespaceContext = createContext<NamespaceContextType | undefined>(undefined);

export const NamespaceProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  // Try to get selected namespaces from localStorage or default to ['all']
  const savedNamespaces = localStorage.getItem('selectedNamespaces');
  const initialNamespaces = savedNamespaces ? JSON.parse(savedNamespaces) : ['all'];

  const [selectedNamespaces, setSelectedNamespaces] = useState<string[]>(initialNamespaces);

  // Whenever selectedNamespaces changes, save it to localStorage
  useEffect(() => {
    localStorage.setItem('selectedNamespaces', JSON.stringify(selectedNamespaces));
  }, [selectedNamespaces]);

  return (
    <NamespaceContext.Provider value={{ selectedNamespaces, setSelectedNamespaces }}>
      {children}
    </NamespaceContext.Provider>
  );
};

export const useNamespace = (): NamespaceContextType => {
  const context = useContext(NamespaceContext);
  if (!context) throw new Error('useNamespace must be used within a NamespaceProvider');
  return context;
};
