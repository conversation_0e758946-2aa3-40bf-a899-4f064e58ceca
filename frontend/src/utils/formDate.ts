// utils/formatDate.ts

export const formatAgeInDays = (isoDate: string): string => {
  const createdDate = new Date(isoDate);
  const now = new Date();

  const diffTime = now.getTime() - createdDate.getTime();
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24)); // milliseconds to days

  const years = Math.floor(diffDays / 365);
  const days = diffDays % 365;

  if (years > 0) {
    return `${years}y${days}d`;
  }

  return `${diffDays} day${diffDays !== 1 ? 's' : ''}`;
};

export const formatTimeAgo = (isoDate: string): string => {
  const date = new Date(isoDate);
  const now = new Date();

  const diffMs = now.getTime() - date.getTime();
  const diffSec = Math.floor(diffMs / 1000);
  const diffMin = Math.floor(diffSec / 60);
  const diffHour = Math.floor(diffMin / 60);
  const diffDay = Math.floor(diffHour / 24);
  const diffYear = Math.floor(diffDay / 365);

  // Less than a minute: show seconds
  if (diffSec < 60) {
    return `${diffSec}s`;
  }
  // Less than an hour: show minutes and seconds
  else if (diffMin < 60) {
    const remainingSec = diffSec % 60;
    return `${diffMin}m ${remainingSec}s`;
  }
  // Less than a day: show hours and minutes
  else if (diffHour < 24) {
    const remainingMin = diffMin % 60;
    return `${diffHour}h ${remainingMin}m`;
  }
  // Less than a year: show days and hours
  else if (diffDay < 365) {
    const remainingHour = diffHour % 24;
    return `${diffDay}d ${remainingHour}h`;
  }
  // More than a year: show years and days
  else {
    const remainingDays = diffDay % 365;
    return `${diffYear}y ${remainingDays}d`;
  }
};
