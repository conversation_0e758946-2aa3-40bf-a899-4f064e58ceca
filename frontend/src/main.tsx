import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import './index.css';
import App from './App.tsx';
import { LoadingProvider } from '../src/contexts/LoadingContext.tsx'; // Import the LoadingProvider
import LoadingBar from './components/UI/LoadingBar.tsx';
import { NamespaceProvider } from './contexts/NamespaceContext.tsx';

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <LoadingProvider>
      <NamespaceProvider>
      <LoadingBar />
      <App />
      </NamespaceProvider>
    </LoadingProvider>
  </StrictMode>,
);
