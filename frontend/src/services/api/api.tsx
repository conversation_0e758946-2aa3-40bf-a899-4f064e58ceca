import axios from 'axios'

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'https://backend.redalerts.tn/api'

// Configure axios defaults
axios.defaults.baseURL = API_BASE_URL;
axios.defaults.headers.common['Accept'] = 'application/json';
axios.defaults.headers.common['Content-Type'] = 'application/json';
axios.defaults.withCredentials = true; // Important for cookies/CSRF

// Create a function to ensure the auth token is set before each request
const ensureAuthToken = () => {
  const token = localStorage.getItem('auth_token');
  if (token) {
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    return true;
  }
  return false;
}

// Initialize with token if available
ensureAuthToken();

// Function to get the selected cluster from localStorage
const getSelectedCluster = () => {
  return localStorage.getItem('selectedCluster');
}

export const getNodes = async () => {
  const selectedCluster = getSelectedCluster(); // Get the selected cluster

  if (!selectedCluster) {
    throw new Error('No cluster selected');
  }

  try {
    const response = await axios.get(`${API_BASE_URL}/${selectedCluster}/nodes`); // Use selectedCluster in the URL
    return response.data;
  } catch (error) {
    throw error;
  }
}

export const getClusters = async () => {
  try {
    // Ensure auth token is set before making the request
    ensureAuthToken();

    console.log('Fetching clusters from API...');
    const response = await axios.get(`${API_BASE_URL}/clusters`); // The clusters endpoint does not depend on selectedCluster
    console.log('Clusters response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching clusters:', error);
    throw error;
  }
}

export const checkClusterExists = async (clusterName: string) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/check-cluster/${clusterName}`);
    return response.data;
  } catch (error) {
    throw error;
  }
}

export const uploadKubeconfig = async (file: File, clusterName: string) => {
  try {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('clusterName', clusterName);

    const response = await axios.post(`${API_BASE_URL}/upload-kubeconfig`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });

    return response.data;
  } catch (error) {
    throw error;
  }
}

// get pods details
export const getPods = async () => {
  const selectedCluster = getSelectedCluster(); // Get the selected cluster

  if (!selectedCluster) {
    throw new Error('No cluster selected');
  }

  try {
    const response = await axios.get(`${API_BASE_URL}/${selectedCluster}/pods`); // Use selectedCluster in the URL
    return response.data;
  } catch (error) {
    throw error;
  }
}
// get deployments details

export const getDeployments = async () => {
  const selectedCluster = getSelectedCluster(); // Get the selected cluster

  if (!selectedCluster) {
    throw new Error('No cluster selected');
  }

  try {
    const response = await axios.get(`${API_BASE_URL}/${selectedCluster}/deployments`);
    return response.data;
  } catch (error) {
    throw error;
  }
}

// get deployments details

export const getDaemonSets = async () => {
  const selectedCluster = getSelectedCluster(); // Get the selected cluster

  if (!selectedCluster) {
    throw new Error('No cluster selected');
  }

  try {
    const response = await axios.get(`${API_BASE_URL}/${selectedCluster}/daemonsets`);
    return response.data;
  } catch (error) {
    throw error;
  }
}

// get stateful Sets details

export const getStatefulSets = async () => {
  const selectedCluster = getSelectedCluster(); // Get the selected cluster

  if (!selectedCluster) {
    throw new Error('No cluster selected');
  }

  try {
    const response = await axios.get(`${API_BASE_URL}/${selectedCluster}/statefulsets`);
    return response.data;
  } catch (error) {
    throw error;
  }
}

// get Replicas details

export const getReplicaSets = async () => {
  const selectedCluster = getSelectedCluster(); // Get the selected cluster

  if (!selectedCluster) {
    throw new Error('No cluster selected');
  }

  try {
    const response = await axios.get(`${API_BASE_URL}/${selectedCluster}/replicasets`);
    return response.data;
  } catch (error) {
    throw error;
  }
}

// get Replication controllers details

export const getReplicationControllers = async () => {
  const selectedCluster = getSelectedCluster(); // Get the selected cluster

  if (!selectedCluster) {
    throw new Error('No cluster selected');
  }

  try {
    const response = await axios.get(`${API_BASE_URL}/${selectedCluster}/replicationcontrollers`);
    return response.data;
  } catch (error) {
    throw error;
  }
}

// get Jobs details

export const getJobs = async () => {
  const selectedCluster = getSelectedCluster(); // Get the selected cluster

  if (!selectedCluster) {
    throw new Error('No cluster selected');
  }

  try {
    const response = await axios.get(`${API_BASE_URL}/${selectedCluster}/jobs`);
    return response.data;
  } catch (error) {
    throw error;
  }
}

// get Jobs details

export const getCronJobs = async () => {
  const selectedCluster = getSelectedCluster(); // Get the selected cluster

  if (!selectedCluster) {
    throw new Error('No cluster selected');
  }

  try {
    const response = await axios.get(`${API_BASE_URL}/${selectedCluster}/cronjobs`);
    return response.data;
  } catch (error) {
    throw error;
  }
}

// get Config Maps details

export const getConfigMaps = async () => {
  const selectedCluster = getSelectedCluster(); // Get the selected cluster

  if (!selectedCluster) {
    throw new Error('No cluster selected');
  }

  try {
    const response = await axios.get(`${API_BASE_URL}/${selectedCluster}/configmaps`);
    return response.data;
  } catch (error) {
    throw error;
  }
}

// get Secrets details

export const getSecrets = async () => {
  const selectedCluster = getSelectedCluster(); // Get the selected cluster

  if (!selectedCluster) {
    throw new Error('No cluster selected');
  }

  try {
    const response = await axios.get(`${API_BASE_URL}/${selectedCluster}/secrets`);
    return response.data;
  } catch (error) {
    throw error;
  }
}

// get Resouce Quotas details

export const getResourceQuotas = async (namespace: string) => {
  const selectedCluster = getSelectedCluster(); // Get the selected cluster

  if (!selectedCluster) {
    throw new Error('No cluster selected');
  }

  try {
    const response = await axios.get(`${API_BASE_URL}/${selectedCluster}/namespaces/${namespace}/resourcequotas`);
    return response.data;
  } catch (error) {
    throw error;
  }
};

// get LimitRanges details

export const getLimitRanges = async () => {
  const selectedCluster = getSelectedCluster(); // Get the selected cluster

  if (!selectedCluster) {
    throw new Error('No cluster selected');
  }

  try {
    const response = await axios.get(`${API_BASE_URL}/${selectedCluster}/limitranges`);
    return response.data;
  } catch (error) {
    throw error;
  }
}

// get Horizontal Pod Autoscalers details

export const getHorizontalPodAutoscalers = async () => {
  const selectedCluster = getSelectedCluster(); // Get the selected cluster

  if (!selectedCluster) {
    throw new Error('No cluster selected');
  }

  try {
    const response = await axios.get(`${API_BASE_URL}/${selectedCluster}/horizontalpodautoscalers`);
    return response.data;
  } catch (error) {
    throw error;
  }
}

// get Horizontal Pod Autoscalers details

export const getServices = async () => {
  const selectedCluster = getSelectedCluster(); // Get the selected cluster

  if (!selectedCluster) {
    throw new Error('No cluster selected');
  }

  try {
    const response = await axios.get(`${API_BASE_URL}/${selectedCluster}/services`);
    return response.data;
  } catch (error) {
    throw error;
  }
}

// get Endpoints details

export const getEndpoints = async () => {
  const selectedCluster = getSelectedCluster(); // Get the selected cluster

  if (!selectedCluster) {
    throw new Error('No cluster selected');
  }

  try {
    const response = await axios.get(`${API_BASE_URL}/${selectedCluster}/endpoints`);
    return response.data;
  } catch (error) {
    throw error;
  }
}

// get Ingresses details

export const getIngresses = async () => {
  const selectedCluster = getSelectedCluster(); // Get the selected cluster

  if (!selectedCluster) {
    throw new Error('No cluster selected');
  }

  try {
    const response = await axios.get(`${API_BASE_URL}/${selectedCluster}/ingresses`);
    return response.data;
  } catch (error) {
    throw error;
  }
}

// get Ingresses Classes details

export const getIngresseClass = async () => {
  const selectedCluster = getSelectedCluster();

  if (!selectedCluster) {
    throw new Error('No cluster selected');
  }

  try {
    const response = await axios.get(`${API_BASE_URL}/${selectedCluster}/ingressclasses`);
    return response.data;
  } catch (error) {
    throw error;
  }
}

// get Network Policies details

export const getNetworkPolicies = async () => {
  const selectedCluster = getSelectedCluster();

  if (!selectedCluster) {
    throw new Error('No cluster selected');
  }

  try {
    const response = await axios.get(`${API_BASE_URL}/${selectedCluster}/networkpolicies`);
    return response.data;
  } catch (error) {
    throw error;
  }
}

// get Persistant Volume Claims details

export const getPersistentVolumeClaims = async () => {
  const selectedCluster = getSelectedCluster();

  if (!selectedCluster) {
    throw new Error('No cluster selected');
  }

  try {
    const response = await axios.get(`${API_BASE_URL}/${selectedCluster}/persistentvolumeclaims`);
    return response.data;
  } catch (error) {
    throw error;
  }
}

// get Persistant Volume details

export const getPersistentVolumes = async () => {
  const selectedCluster = getSelectedCluster();

  if (!selectedCluster) {
    throw new Error('No cluster selected');
  }

  try {
    const response = await axios.get(`${API_BASE_URL}/${selectedCluster}/persistentvolumes`);
    return response.data;
  } catch (error) {
    throw error;
  }
}

// get Persistant Storage Classes details

export const getStorageClasses = async () => {
  const selectedCluster = getSelectedCluster();

  if (!selectedCluster) {
    throw new Error('No cluster selected');
  }

  try {
    const response = await axios.get(`${API_BASE_URL}/${selectedCluster}/storageclasses`);
    return response.data;
  } catch (error) {
    throw error;
  }
}


// get Name Spaces details

export const getNamespaces = async () => {
  const selectedCluster = getSelectedCluster(); // Get the selected cluster

  if (!selectedCluster) {
    throw new Error('No cluster selected');
  }

  try {
    const response = await axios.get(`${API_BASE_URL}/${selectedCluster}/namespaces`);
    return response.data;
  } catch (error) {
    throw error;
  }
}

// get Events details

export const getEvents = async () => {
  const selectedCluster = getSelectedCluster(); // Get the selected cluster

  if (!selectedCluster) {
    throw new Error('No cluster selected');
  }

  try {
    const response = await axios.get(`${API_BASE_URL}/${selectedCluster}/events`);
    return response.data;
  } catch (error) {
    throw error;
  }
}

// Access Control API functions

// get Service Accounts details
export const getServiceAccounts = async () => {
  const selectedCluster = getSelectedCluster();

  if (!selectedCluster) {
    throw new Error('No cluster selected');
  }

  try {
    const response = await axios.get(`${API_BASE_URL}/${selectedCluster}/serviceaccounts`);
    return response.data;
  } catch (error) {
    throw error;
  }
}

// get Cluster Roles details
export const getClusterRoles = async () => {
  const selectedCluster = getSelectedCluster();

  if (!selectedCluster) {
    throw new Error('No cluster selected');
  }

  try {
    const response = await axios.get(`${API_BASE_URL}/${selectedCluster}/clusterroles`);
    return response.data;
  } catch (error) {
    throw error;
  }
}

// get Roles details
export const getRoles = async () => {
  const selectedCluster = getSelectedCluster();

  if (!selectedCluster) {
    throw new Error('No cluster selected');
  }

  try {
    const response = await axios.get(`${API_BASE_URL}/${selectedCluster}/roles`);
    return response.data;
  } catch (error) {
    throw error;
  }
}

// get Cluster Role Bindings details
export const getClusterRoleBindings = async () => {
  const selectedCluster = getSelectedCluster();

  if (!selectedCluster) {
    throw new Error('No cluster selected');
  }

  try {
    const response = await axios.get(`${API_BASE_URL}/${selectedCluster}/clusterrolebindings`);
    return response.data;
  } catch (error) {
    throw error;
  }
}

// get Role Bindings details
export const getRoleBindings = async () => {
  const selectedCluster = getSelectedCluster();

  if (!selectedCluster) {
    throw new Error('No cluster selected');
  }

  try {
    const response = await axios.get(`${API_BASE_URL}/${selectedCluster}/rolebindings`);
    return response.data;
  } catch (error) {
    throw error;
  }
}

// Custom Resources API functions

// get Custom Resource Definitions details
export const getCustomResourceDefinitions = async () => {
  const selectedCluster = getSelectedCluster();

  if (!selectedCluster) {
    throw new Error('No cluster selected');
  }

  try {
    const response = await axios.get(`${API_BASE_URL}/${selectedCluster}/customresourcedefinitions`);
    return response.data;
  } catch (error) {
    throw error;
  }
}

// Cert Manager API functions

// get Certificates details
export const getCertificates = async () => {
  const selectedCluster = getSelectedCluster();

  if (!selectedCluster) {
    throw new Error('No cluster selected');
  }

  try {
    const response = await axios.get(`${API_BASE_URL}/${selectedCluster}/certificates`);
    return response.data;
  } catch (error) {
    throw error;
  }
}

// get Certificate Requests details
export const getCertificateRequests = async () => {
  const selectedCluster = getSelectedCluster();

  if (!selectedCluster) {
    throw new Error('No cluster selected');
  }

  try {
    const response = await axios.get(`${API_BASE_URL}/${selectedCluster}/certificaterequests`);
    return response.data;
  } catch (error) {
    throw error;
  }
}

// get Issuers details
export const getIssuers = async () => {
  const selectedCluster = getSelectedCluster();

  if (!selectedCluster) {
    throw new Error('No cluster selected');
  }

  try {
    const response = await axios.get(`${API_BASE_URL}/${selectedCluster}/issuers`);
    return response.data;
  } catch (error) {
    throw error;
  }
}

// get Cluster Issuers details
export const getClusterIssuers = async () => {
  const selectedCluster = getSelectedCluster();

  if (!selectedCluster) {
    throw new Error('No cluster selected');
  }

  try {
    const response = await axios.get(`${API_BASE_URL}/${selectedCluster}/clusterissuers`);
    return response.data;
  } catch (error) {
    throw error;
  }
}

// ACME API functions

// get Challenges details
export const getChallenges = async () => {
  const selectedCluster = getSelectedCluster();

  if (!selectedCluster) {
    throw new Error('No cluster selected');
  }

  try {
    const response = await axios.get(`${API_BASE_URL}/${selectedCluster}/challenges`);
    return response.data;
  } catch (error) {
    throw error;
  }
}

// get Orders details
export const getOrders = async () => {
  const selectedCluster = getSelectedCluster();

  if (!selectedCluster) {
    throw new Error('No cluster selected');
  }

  try {
    const response = await axios.get(`${API_BASE_URL}/${selectedCluster}/orders`);
    return response.data;
  } catch (error) {
    throw error;
  }
}

// Helm API functions

// get Helm Charts details
export const getHelmCharts = async () => {
  const selectedCluster = getSelectedCluster();

  if (!selectedCluster) {
    throw new Error('No cluster selected');
  }

  try {
    const response = await axios.get(`${API_BASE_URL}/${selectedCluster}/helmcharts`);
    return response.data;
  } catch (error) {
    throw error;
  }
}

// get Helm Releases details
export const getHelmReleases = async () => {
  const selectedCluster = getSelectedCluster();

  if (!selectedCluster) {
    throw new Error('No cluster selected');
  }

  try {
    const response = await axios.get(`${API_BASE_URL}/${selectedCluster}/helmreleases`);
    return response.data;
  } catch (error) {
    throw error;
  }
}