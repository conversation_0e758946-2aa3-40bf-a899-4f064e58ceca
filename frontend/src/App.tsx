import React, { useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate} from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import Sidebar from './components/layout/Sidebar';
import Navbar from './components/layout/Navbar';
import NodeList from './pages/nodes/NodeList';
import Dashboard from './pages/Dashboard';
import MainDashboard from './pages/MainDashboard';
import ProtectedRoute from './components/layout/ProtectedRoute';
import Login from './pages/Login';
import PodsList from './pages/Workloads/PodsList';
//import Cluster from './pages/Cluster';
import LandingPage from './pages/LandingPage';
import Deployments from './pages/Workloads/Deployments';
import DaemonSets from './pages/Workloads/DaemoneSets';
import StatefulSets from './pages/Workloads/StatefulSets';
import Replicas from './pages/Workloads/Replicas';
import ReplicationControllers from './pages/Workloads/ReplicationControllers';
import Jobs from './pages/Workloads/Jobs';
import CronJobs from './pages/Workloads/CronJobs';
import ConfigMaps from './pages/Config/ConfigMaps';
import Secrets from './pages/Config/Secrets';
import ResourceQuotas from './pages/Config/ResourceQuotas';
import LimitRanges from './pages/Config/LimitRanges';
import HorizontalPod from './pages/Config/HorizontalPods';
import Services from './pages/network/Services';
import Endpoints from './pages/network/Endpoints';
import Ingresses from './pages/network/Ingresses';
import IngresseClasses from './pages/network/IngresseClasses';
import NetworkPolicies from './pages/network/NetworkPolicies';
import PersistantVolumeClaims from './pages/storage/PersistantVolumeClaims';
import PersistantVolume from './pages/storage/PersistantVolume';
import StorageClasses from './pages/storage/StorageClasses';
import NameSpaces from './pages/namespace/NameSpaces';
import Events from './pages/namespace/Events';
// Access Control imports
import ServiceAccounts from './pages/AccessControl/ServiceAccounts';
import ClusterRoles from './pages/AccessControl/ClusterRoles';
import Roles from './pages/AccessControl/Roles';
import ClusterRoleBindings from './pages/AccessControl/ClusterRoleBindings';
import RoleBindings from './pages/AccessControl/RoleBindings';
// Helm imports
import HelmCharts from './pages/Helm/HelmCharts';
import HelmReleases from './pages/Helm/HelmReleases';
// Custom Resources imports
import Definitions from './pages/CustomResources/Definitions';
// ACME imports
import Challenges from './pages/CustomResources/ACME/Challenges';
import Orders from './pages/CustomResources/ACME/Orders';
// Cert Manager imports
import Certificates from './pages/CustomResources/CertManager/Certificates';
import CertificateRequests from './pages/CustomResources/CertManager/CertificateRequests';
import Issuers from './pages/CustomResources/CertManager/Issuers';
import ClusterIssuers from './pages/CustomResources/CertManager/ClusterIssuers';

// Create an AppContent component that uses the auth context
const AppContent: React.FC = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { isAuthenticated, loading } = useAuth();

  // Sync the auth context state with our local state


  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-red-600 border-t-transparent rounded-full animate-spin mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <Routes>
      <Route path="/" element={<LandingPage />} />
      <Route
        path="/login"
        element={isAuthenticated ? <Navigate to="/main-dashboard" replace /> : <Login />}
      />
      <Route path="/main-dashboard" element={isAuthenticated ? <MainDashboard /> : <Navigate to="/login" replace />} />

      {/* Routes avec Sidebar + Navbar */}
      <Route
        path="*"
        element={
          isAuthenticated ? (
            <div className="flex">
              <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
              <div className="flex-1 flex flex-col min-w-0">
                <Navbar onToggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
                <main className="flex-1 bg-gray-100 min-h-screen overflow-auto">
                  <Routes>
                    <Route path="/main-dashboard" element={<MainDashboard />} />
                    <Route path="/dashboard" element={<Dashboard />} />
                    <Route element={<ProtectedRoute />}>
                      <Route path="/nodes" element={<NodeList />} />
                      <Route path="/pods" element={<PodsList />} />
                      <Route path="/deployments" element={<Deployments />} />
                      <Route path="/daemonsets" element={<DaemonSets />} />
                      <Route path="/statefulSets" element={<StatefulSets />} />
                      <Route path="/replicasets" element={<Replicas />} />
                      <Route path="/replicationcontrollers" element={<ReplicationControllers />} />
                      <Route path="/Jobs" element={<Jobs />} />
                      <Route path="/cronjobs" element={<CronJobs />} />
                      <Route path="/configmaps" element={<ConfigMaps />} />
                      <Route path="/secrets" element={<Secrets />} />
                      <Route path="/resourcequotas" element={<ResourceQuotas />} />
                      <Route path="/limitRanges" element={<LimitRanges />} />
                      <Route path="/horizontalpodautoscalers" element={<HorizontalPod />} />
                      <Route path="/services" element={<Services />} />
                      <Route path="/endpoints" element={<Endpoints />} />
                      <Route path="/ingresses" element={<Ingresses />} />
                      <Route path="/ingressclasses" element={<IngresseClasses />} />
                      <Route path="/networkpolicies" element={<NetworkPolicies />} />
                      <Route path="/persistentvolumeclaims" element={<PersistantVolumeClaims/>} />
                      <Route path="/persistentvolumes" element={<PersistantVolume/>} />
                      <Route path="/storageclasses" element={<StorageClasses/>} />
                      <Route path="/namespaces" element={<NameSpaces/>} />
                      <Route path="/events" element={<Events/>} />
                      {/* Access Control Routes */}
                      <Route path="/serviceaccounts" element={<ServiceAccounts />} />
                      <Route path="/clusterroles" element={<ClusterRoles />} />
                      <Route path="/roles" element={<Roles />} />
                      <Route path="/clusterrolebindings" element={<ClusterRoleBindings />} />
                      <Route path="/rolebindings" element={<RoleBindings />} />

                      {/* Custom Resources Routes */}
                      <Route path="/definitions" element={<Definitions />} />

                      {/* ACME Routes */}
                      <Route path="/challenges" element={<Challenges />} />
                      <Route path="/orders" element={<Orders />} />

                      {/* Cert Manager Routes */}
                      <Route path="/certificates" element={<Certificates />} />
                      <Route path="/certificaterequests" element={<CertificateRequests />} />
                      <Route path="/issuers" element={<Issuers />} />
                      <Route path="/clusterissuers" element={<ClusterIssuers />} />

                      {/* Helm Routes */}
                      <Route path="/helmcharts" element={<HelmCharts />} />
                      <Route path="/helmreleases" element={<HelmReleases />} />
                    </Route>
                  </Routes>
                </main>
              </div>
            </div>
          ) : (
            <Navigate to="/login" replace />
          )
        }
      />
    </Routes>
  );
};

// Main App component that provides the AuthProvider
const App: React.FC = () => {
  return (
    <Router>
      <AuthProvider>
        <ToastContainer position="top-right" autoClose={3000} hideProgressBar={false} />
        <AppContent />
      </AuthProvider>
    </Router>
  );
};

export default App;
