// components/LoadingBar.tsx
import React from "react";
import { useLoading } from "../../contexts/LoadingContext";
import { LinearProgress } from "@mui/material";
import Box from "@mui/material/Box";

const LoadingBar: React.FC = () => {
  const { isLoading } = useLoading();

  return (
    <Box
      sx={{
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        zIndex: 9999,
        height: 4,
        width: "100%",
      }}
    >
      {isLoading && (
        <LinearProgress
          sx={{
            height: "4px",
            backgroundColor: "#e0e0e0", 
            "& .MuiLinearProgress-bar": {
              backgroundColor: "rgb(220 38 38)", 
            },
          }}
        />
      )}
    </Box>
  );
};

export default LoadingBar;
