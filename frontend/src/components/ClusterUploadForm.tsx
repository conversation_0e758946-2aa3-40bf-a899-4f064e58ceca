import React, { useState, useRef } from 'react';
import {
  Box,
  Button,
  TextField,
  Typography,
  Paper,
  Alert,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle
} from '@mui/material';
import { uploadKubeconfig, checkClusterExists } from '../services/api/api';

interface ClusterUploadFormProps {
  onUploadSuccess: () => void;
}

const ClusterUploadForm: React.FC<ClusterUploadFormProps> = ({ onUploadSuccess }) => {
  const [clusterName, setClusterName] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files.length > 0) {
      setSelectedFile(event.target.files[0]);
    }
  };

  const handleClusterNameChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setClusterName(event.target.value);
  };

  const validateForm = () => {
    if (!clusterName) {
      setError('Cluster name is required');
      return false;
    }

    if (!/^[a-zA-Z0-9_-]+$/.test(clusterName)) {
      setError('Cluster name can only contain letters, numbers, underscores, and hyphens');
      return false;
    }

    if (!selectedFile) {
      setError('Please select a kubeconfig file');
      return false;
    }

    return true;
  };

  const checkIfClusterExists = async () => {
    if (!validateForm()) return;

    setLoading(true);
    setError(null);

    try {
      const response = await checkClusterExists(clusterName);
      if (response.exists) {
        setShowConfirmDialog(true);
      } else {
        handleUpload();
      }
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to check if cluster exists');
    } finally {
      setLoading(false);
    }
  };

  const handleUpload = async () => {
    if (!validateForm() || !selectedFile) return;

    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await uploadKubeconfig(selectedFile, clusterName);

      if (response.success) {
        setSuccess(response.message || 'Kubeconfig uploaded successfully');
        setClusterName('');
        setSelectedFile(null);
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
        onUploadSuccess();
      } else {
        setError(response.message || 'Failed to upload kubeconfig');
      }
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to upload kubeconfig');
    } finally {
      setLoading(false);
      setShowConfirmDialog(false);
    }
  };

  const handleConfirmDialogClose = () => {
    setShowConfirmDialog(false);
  };

  return (
    <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
      <Typography variant="h5" gutterBottom fontWeight="bold">
        Upload New Cluster
      </Typography>

      <Box component="form" sx={{ mt: 2 }}>
        <TextField
          fullWidth
          label="Cluster Name"
          variant="outlined"
          value={clusterName}
          onChange={handleClusterNameChange}
          margin="normal"
          helperText="Use only letters, numbers, underscores, and hyphens"
          disabled={loading}
        />

        <Box sx={{ mt: 2, mb: 2 }}>
          <input
            ref={fileInputRef}
            accept=".yaml,.yml,*"
            style={{ display: 'none' }}
            id="kubeconfig-file"
            type="file"
            onChange={handleFileChange}
            disabled={loading}
          />
          <label htmlFor="kubeconfig-file">
            <Button
              variant="outlined"
              component="span"
              disabled={loading}
            >
              Select Kubeconfig File
            </Button>
          </label>
          {selectedFile && (
            <Typography variant="body2" sx={{ mt: 1 }}>
              Selected file: {selectedFile.name}
            </Typography>
          )}
        </Box>

        {error && (
          <Alert severity="error" sx={{ mt: 2, mb: 2 }}>
            {error}
          </Alert>
        )}

        {success && (
          <Alert severity="success" sx={{ mt: 2, mb: 2 }}>
            {success}
          </Alert>
        )}

        <Button
          variant="contained"
          color="primary"
          onClick={checkIfClusterExists}
          disabled={loading}
          sx={{ mt: 2 }}
        >
          {loading ? <CircularProgress size={24} /> : 'Upload Kubeconfig'}
        </Button>
      </Box>

      {/* Confirmation Dialog */}
      <Dialog
        open={showConfirmDialog}
        onClose={handleConfirmDialogClose}
      >
        <DialogTitle>Cluster Already Exists</DialogTitle>
        <DialogContent>
          <DialogContentText>
            A cluster with the name "{clusterName}" already exists. Do you want to overwrite it?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleConfirmDialogClose} color="primary">
            Cancel
          </Button>
          <Button onClick={handleUpload} color="error" autoFocus>
            Overwrite
          </Button>
        </DialogActions>
      </Dialog>
    </Paper>
  );
};

export default ClusterUploadForm;
