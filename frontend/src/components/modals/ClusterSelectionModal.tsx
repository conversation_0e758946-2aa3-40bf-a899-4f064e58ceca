import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogTitle,
  <PERSON>alogContent,
  <PERSON>alog<PERSON>ctions,
  Button,
  Typography,
  Box,
  Card,
  CardContent,
  CardActionArea,
  CircularProgress,
  Divider,
  Collapse
} from '@mui/material';
import { useClusters, ClusterInfo } from '../../hooks/cluster/useClusters';
import useSelectedCluster from '../../hooks/cluster/useSelectedCluster';
import ClusterUploadForm from '../ClusterUploadForm';
import { formatDistanceToNow } from 'date-fns';

interface ClusterSelectionModalProps {
  open: boolean;
  onClose: () => void;
}

const ClusterSelectionModal: React.FC<ClusterSelectionModalProps> = ({ open, onClose }) => {
  const { clusters, loading, error, refetch } = useClusters();
  const [selectedCluster, setSelectedCluster] = useSelectedCluster();
  const [showUploadForm, setShowUploadForm] = useState(false);

  const handleSelectCluster = (cluster: ClusterInfo) => {
    // Only refresh if selecting a different cluster
    if (selectedCluster !== cluster.name) {
      setSelectedCluster(cluster.name);
      onClose();

      // Refresh the current page to load data for the newly selected cluster
      window.location.reload();
    } else {
      // Just close the modal if selecting the same cluster
      onClose();
    }
  };

  const handleUploadSuccess = () => {
    refetch();
    setShowUploadForm(false);
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
    >
      <DialogTitle>
        <Typography variant="h5" fontWeight="bold">
          Select Kubernetes Cluster
        </Typography>
      </DialogTitle>
      <DialogContent>
        {loading ? (
          <Box display="flex" justifyContent="center" my={4}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Box textAlign="center" my={2}>
            <Typography color="error">{error}</Typography>
          </Box>
        ) : clusters.length === 0 ? (
          <Box textAlign="center" my={4}>
            <Typography variant="body1" color="textSecondary">
              No clusters available. Please upload a kubeconfig file.
            </Typography>
          </Box>
        ) : (
          <Box my={2}>
            <Typography variant="body1" color="textSecondary" mb={2}>
              Select one of the clusters below to monitor its metrics, pods, and health.
            </Typography>
            <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(12, 1fr)', gap: 2 }}>
              {clusters.map((cluster, index) => {
                const isSelected = selectedCluster === cluster.name;
                return (
                  <Box key={index} sx={{ gridColumn: { xs: 'span 12', sm: 'span 6', md: 'span 4' } }}>
                    <Card
                      sx={{
                        height: 120,
                        display: 'flex',
                        flexDirection: 'column',
                        justifyContent: 'center',
                        border: isSelected ? '2px solid #e53935' : '1px solid #ddd',
                        backgroundColor: isSelected ? '#ffebee' : '#fafafa',
                        transition: '0.3s',
                        boxShadow: isSelected ? 3 : 1,
                        '&:hover': {
                          boxShadow: 3,
                        },
                      }}
                    >
                      <CardActionArea
                        sx={{ height: '100%' }}
                        onClick={() => handleSelectCluster(cluster)}
                      >
                        <CardContent
                          sx={{
                            display: 'flex',
                            flexDirection: 'column',
                            justifyContent: 'center',
                            alignItems: 'center',
                            height: '100%',
                          }}
                        >
                          <Typography variant="h6" fontWeight="bold">
                            {cluster.name}
                          </Typography>
                          <Typography variant="body2" color="textSecondary" mt={1}>
                            Uploaded {formatDistanceToNow(new Date(cluster.upload_time))} ago
                          </Typography>
                        </CardContent>
                      </CardActionArea>
                    </Card>
                  </Box>
                );
              })}
            </Box>
          </Box>
        )}

        <Divider sx={{ my: 3 }} />

        <Box>
          <Button
            variant="outlined"
            color="primary"
            onClick={() => setShowUploadForm(!showUploadForm)}
            sx={{ mb: 2 }}
          >
            {showUploadForm ? 'Hide Upload Form' : 'Upload New Cluster'}
          </Button>

          <Collapse in={showUploadForm}>
            <ClusterUploadForm onUploadSuccess={handleUploadSuccess} />
          </Collapse>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="primary">
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ClusterSelectionModal;
