import React from 'react';

interface ClusterRequiredModalProps {
  onClose: () => void;
}

const ClusterRequiredModal: React.FC<ClusterRequiredModalProps> = ({ onClose }) => {

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white p-6 rounded shadow-lg text-center max-w-md">
        <h2 className="text-xl font-semibold mb-4">Cluster Not Selected</h2>
        <p className="mb-4">You need to select a Kubernetes cluster before accessing this page.</p>
        <p className="mb-6 text-gray-600 text-sm">
          This feature requires an active Kubernetes cluster connection. Please select a cluster from the dropdown in the navigation bar (top of the page) or upload a new one.
        </p>
        <div className="flex justify-center space-x-4">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition duration-200"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default ClusterRequiredModal;
