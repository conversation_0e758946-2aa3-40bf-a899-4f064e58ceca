import {
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Checkbox,
  ListItemText,
  OutlinedInput,
} from '@mui/material';
import { useNamespace } from '../contexts/NamespaceContext';
import { useFilterNameSpace } from '../hooks/filter/useFilterNameSpaces';

const NamespaceFilter: React.FC = () => {
  const { selectedNamespaces, setSelectedNamespaces } = useNamespace();
  const { namespace } = useFilterNameSpace();

  // Make sure 'namespace' is an array of namespaces
  const namespaces = namespace?.map((ns: any) => ns.metadata.name) || [];

  const handleChange = (event: any) => {
    const value = event.target.value as string[];

    // If "all" is selected, clear all other selections
    if (value[value.length - 1] === 'all') {
      setSelectedNamespaces(['all']);
      return;
    }

    // If any other namespace is selected, remove "all" if it's present
    if (selectedNamespaces.includes('all') && value.length > 0) {
      setSelectedNamespaces(value.filter(v => v !== 'all'));
      return;
    }

    // Normal selection
    setSelectedNamespaces(value);
  };

  const renderValue = (selected: string[]) => {
    // If "all" is selected, display "All Namespaces"
    if (selected.includes('all')) {
      return 'All Namespaces';
    }
    // Otherwise, display the number of selected namespaces
    return `${selected.length} namespace(s) selected`;
  };

  return (
    <FormControl sx={{ minWidth: 250 }}>
      <InputLabel>Namespaces</InputLabel>
      <Select
        multiple
        value={selectedNamespaces}
        onChange={handleChange}
        input={<OutlinedInput label="Namespaces" />}
        renderValue={renderValue}
      >
        <MenuItem value="all">
          <Checkbox checked={selectedNamespaces.includes('all')} />
          <ListItemText primary="All Namespaces" />
        </MenuItem>
        {namespaces.map((ns) => (
          <MenuItem key={ns} value={ns} disabled={false}>
            <Checkbox checked={selectedNamespaces.includes(ns)} />
            <ListItemText primary={ns} />
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
};

export default NamespaceFilter;
