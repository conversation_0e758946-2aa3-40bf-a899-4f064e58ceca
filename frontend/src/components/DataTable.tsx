// components/DataTable.tsx
import React from 'react';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import Paper from '@mui/material/Paper';
import Box from '@mui/material/Box';
import Skeleton from '@mui/material/Skeleton';

interface DataTableProps {
  rows: any[];
  columns: GridColDef[];
  loading?: boolean;
  error?: string | null;
  title?: string;
  onRowClick?: (params: any) => void;
}

const DataTable: React.FC<DataTableProps> = ({ rows, columns, loading = false, error = null, onRowClick }) => {
  if (loading) {
    return (
      <Box sx={{ p: 2 }}>
        <Paper sx={{ height: 'calc(100vh - 120px)', width: '100%', p: 2 }}>
          {[...Array(10)].map((_, i) => (
            <Box key={i} sx={{ display: 'flex', mb: 1 }}>
              {columns.map((_, index) => (
                <Skeleton key={index} variant="rectangular" width={150} height={30} sx={{ mr: 2 }} />
              ))}
            </Box>
          ))}
        </Paper>
      </Box>
    );
  }

  if (error) return <div className="p-4 text-red-500">{error}</div>;

  return (
    <Box sx={{ p: 2 }}>
      <Paper sx={{ height: 'calc(100vh - 120px)', width: '100%' }}>
        <DataGrid
          rows={rows}
          columns={columns}
          onRowClick={onRowClick}
          initialState={{ pagination: { paginationModel: { page: 0, pageSize: 25 } } }}
          pageSizeOptions={[25, 50, 100]}
          checkboxSelection
          sx={{ border: 0 }}
        />
      </Paper>
    </Box>
  );
};

export default DataTable;
