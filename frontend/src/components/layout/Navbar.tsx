import React, { useState, useRef, useEffect } from 'react';
import { Menu, User, ChevronDown } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import useSelectedCluster from '../../hooks/cluster/useSelectedCluster';
import { useClusters, ClusterInfo } from '../../hooks/cluster/useClusters';
import ClusterSelectionModal from '../modals/ClusterSelectionModal';

interface NavbarProps {
  onToggleSidebar: () => void;

}

const Navbar: React.FC<NavbarProps> = ({ onToggleSidebar }) => {
  const [userDropdownOpen, setUserDropdownOpen] = useState(false);
  const [clusterDropdownOpen, setClusterDropdownOpen] = useState(false);
  const [showClusterModal, setShowClusterModal] = useState(false);
  const userDropdownRef = useRef<HTMLDivElement>(null);
  const clusterDropdownRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();
  const [selectedCluster, setSelectedCluster] = useSelectedCluster();
  const { clusters } = useClusters();

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (userDropdownRef.current && !userDropdownRef.current.contains(event.target as Node)) {
        setUserDropdownOpen(false);
      }
      if (clusterDropdownRef.current && !clusterDropdownRef.current.contains(event.target as Node)) {
        setClusterDropdownOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Use our auth context
  const { logout } = useAuth();

  const handleLogout = async () => {
    setUserDropdownOpen(false);

    try {
      // Use the logout function from our auth context
      await logout();

      // Navigate to login page
      navigate('/login');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const handleSelectCluster = (cluster: ClusterInfo) => {
    // Only refresh if selecting a different cluster
    if (selectedCluster !== cluster.name) {
      setSelectedCluster(cluster.name);
      setClusterDropdownOpen(false);

      // Refresh the current page to load data for the newly selected cluster
      window.location.reload();
    } else {
      // Just close the dropdown if selecting the same cluster
      setClusterDropdownOpen(false);
    }
  };

  return (
    <div className="w-full h-16 bg-white shadow-md flex items-center justify-between px-4">
      <div className="flex items-center space-x-4">
        {/* Hamburger button on mobile */}
        <button onClick={onToggleSidebar} className="md:hidden">
          <Menu className="w-6 h-6 text-gray-700" />
        </button>
        <button
          onClick={() => navigate('/main-dashboard')}
          className="text-sm md:text-base font-medium text-red-600 hover:underline cursor-pointer"
        >
          Back to Dashboard
        </button>

        {/* Cluster selection dropdown */}
        <div className="relative" ref={clusterDropdownRef}>
          <button
            onClick={() => setClusterDropdownOpen((prev) => !prev)}
            className="flex items-center space-x-1 ml-4 pl-4 border-l border-gray-300 focus:outline-none"
          >
            <span className="text-sm text-gray-500">Cluster:</span>
            <span className="ml-2 text-sm font-medium text-red-600">
              {selectedCluster || 'Select a cluster'}
            </span>
            <ChevronDown className="w-4 h-4 text-gray-500" />
          </button>

          {clusterDropdownOpen && (
            <div className="absolute left-0 mt-2 w-64 bg-white border border-gray-200 rounded-md shadow-lg z-50">
              <div className="max-h-60 overflow-y-auto">
                {clusters && clusters.length > 0 ? (
                  clusters.map((cluster, index) => (
                    <button
                      key={index}
                      onClick={() => handleSelectCluster(cluster)}
                      className={`w-full text-left px-4 py-2 hover:bg-gray-100 ${
                        selectedCluster === cluster.name ? 'bg-red-50 text-red-600 font-medium' : 'text-gray-700'
                      }`}
                    >
                      {cluster.name}
                    </button>
                  ))
                ) : (
                  <div className="px-4 py-2 text-gray-500">No clusters available</div>
                )}
              </div>
              <div className="border-t border-gray-200">
                <button
                  onClick={() => {
                    setClusterDropdownOpen(false);
                    setShowClusterModal(true);
                  }}
                  className="w-full text-left px-4 py-2 text-red-600 hover:bg-gray-100 font-medium"
                >
                  Add/Upload Cluster
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* User dropdown */}
      <div className="relative" ref={userDropdownRef}>
        <button
          onClick={() => setUserDropdownOpen((prev) => !prev)}
          className="focus:outline-none"
        >
          <User className="w-6 h-6 text-gray-700" />
        </button>

        {userDropdownOpen && (
          <div className="absolute right-0 mt-2 w-40 bg-white border border-gray-200 rounded-md shadow-lg z-50">
            <button
              onClick={handleLogout}
              className="w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100"
            >
              Déconnexion
            </button>
          </div>
        )}
      </div>

      {/* Cluster Selection Modal */}
      <ClusterSelectionModal
        open={showClusterModal}
        onClose={() => setShowClusterModal(false)}
      />
    </div>
  );
};

export default Navbar;

