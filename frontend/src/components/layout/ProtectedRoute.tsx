import React, { useEffect, useState } from 'react';
import { Outlet, useLocation } from 'react-router-dom';
import ClusterRequiredModal from '../modals/ClusterRequiredModal';

const ProtectedRoute: React.FC = () => {
  // Get the current location to detect route changes
  const location = useLocation();

  // Check for selectedCluster on every render
  const selectedCluster = localStorage.getItem('selectedCluster');
  const [showModal, setShowModal] = useState(false);

  // Update this effect to run on location changes as well
  useEffect(() => {
    if (!selectedCluster) {
      setShowModal(true);
    } else {
      setShowModal(false);
    }
  }, [selectedCluster, location.pathname]);

  const handleCloseModal = () => {
    setShowModal(false);
  };

  // Show the modal if there's no selected cluster and the modal should be shown
  if (!selectedCluster && showModal) {
    return <ClusterRequiredModal onClose={handleCloseModal} />;
  }

  return <Outlet />;
};

export default ProtectedRoute;
