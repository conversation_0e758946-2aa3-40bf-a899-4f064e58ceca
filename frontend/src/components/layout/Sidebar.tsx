import React, { useState, useEffect } from 'react';
import { NavLink } from 'react-router-dom';
import { X, ChevronDown, ChevronRight } from 'lucide-react';

interface SidebarProps {
  isOpen: boolean;
  setIsOpen: (value: boolean) => void;
}

const Sidebar: React.FC<SidebarProps> = ({ isOpen, setIsOpen }) => {

  const [showWorkloads, setShowWorkloads] = useState(() => {
    return localStorage.getItem('showWorkloads') === 'true';
  });

  const [showConfig, setShowConfig] = useState(() => {
    return localStorage.getItem('showConfig') === 'true';
  });

  const [showNetwork, setShowNetwork] = useState(() => {
    return localStorage.getItem('showNetwork') === 'true';
  });

  const [showStorage, setShowStorage] = useState(() => {
    return localStorage.getItem('showStorage') === 'true';
  });

  const [showHelm, setShowHelm] = useState(() => {
    return localStorage.getItem('showHelm') === 'true';
  });

  const [showAccessControl, setShowAccessControl] = useState(() => {
    return localStorage.getItem('showAccessControl') === 'true';
  });

  const [showCustomResources, setShowCustomResources] = useState(() => {
    return localStorage.getItem('showCustomResources') === 'true';
  });

  const [showACME, setShowACME] = useState(() => {
    return localStorage.getItem('showACME') === 'true';
  });

  const [showCertManager, setShowCertManager] = useState(() => {
    return localStorage.getItem('showCertManager') === 'true';
  });

  useEffect(() => {
    localStorage.setItem('showWorkloads', String(showWorkloads));
  }, [showWorkloads]);

  useEffect(() => {
    localStorage.setItem('showConfig', String(showConfig));
  }, [showConfig]);

  useEffect(() => {
    localStorage.setItem('showNetwork', String(showNetwork));
  }, [showNetwork]);

  useEffect(() => {
    localStorage.setItem('showStorage', String(showStorage));
  }, [showStorage]);

  useEffect(() => {
    localStorage.setItem('showHelm', String(showHelm));
  }, [showHelm]);

  useEffect(() => {
    localStorage.setItem('showAccessControl', String(showAccessControl));
  }, [showAccessControl]);

  useEffect(() => {
    localStorage.setItem('showCustomResources', String(showCustomResources));
  }, [showCustomResources]);

  useEffect(() => {
    localStorage.setItem('showACME', String(showACME));
  }, [showACME]);

  useEffect(() => {
    localStorage.setItem('showCertManager', String(showCertManager));
  }, [showCertManager]);

  return (
    <>
      {isOpen && (
        <div
          className="fixed inset-0 bg-white opacity-50 z-30 md:hidden"
          onClick={() => setIsOpen(false)}
        ></div>
      )}

      <div
        className={`fixed z-40 inset-y-0 left-0 w-[300px] bg-[#0F172A] opacity-95 text-white transform ${
          isOpen ? 'translate-x-0' : '-translate-x-full'
        } transition-transform duration-300 ease-in-out md:relative md:translate-x-0`}
      >
        <div className="flex justify-between items-center p-4 md:hidden">
          <h2 className="text-xl font-semibold">Menu</h2>
          <button onClick={() => setIsOpen(false)}>
            <X className="w-6 h-6" />
          </button>
        </div>

        <nav className="flex flex-col space-y-4 p-4">
          <NavLink
            to="/main-dashboard"
            className={({ isActive }) =>
              `p-2 rounded ${isActive ? 'bg-red-700 font-semibold' : 'hover:bg-red-700'}`
            }
            onClick={() => setIsOpen(false)}
          >
            🏠 Main Dashboard
          </NavLink>
          <NavLink
            to="/dashboard"
            className={({ isActive }) =>
              `p-2 rounded ${isActive ? 'bg-red-700 font-semibold' : 'hover:bg-red-700'}`
            }
            onClick={() => setIsOpen(false)}
          >
            📊 Cluster Overview
          </NavLink>
          <NavLink
            to="/nodes"
            className={({ isActive }) =>
              `p-2 rounded ${isActive ? 'bg-red-700 font-semibold' : 'hover:bg-red-700'}`
            }
            onClick={() => setIsOpen(false)}
          >
            🖥️ Nodes
          </NavLink>

          {/* Workloads Section */}
          <div>
            <button
              className="flex items-center justify-between w-full p-2 rounded hover:bg-red-700"
              onClick={() => setShowWorkloads(!showWorkloads)}
            >
              <span>🧱  Workloads</span>
              {showWorkloads ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
            </button>

            {showWorkloads && (
              <div className="ml-4 mt-2 flex flex-col space-y-2">

                <NavLink to="/pods" className={({ isActive }) =>
                  `p-2 rounded ${isActive ? 'bg-red-700 font-semibold' : 'hover:bg-red-700'}`
                } onClick={() => setIsOpen(false)}>📦 Pods</NavLink>

                <NavLink to="/deployments" className={({ isActive }) =>
                  `p-2 rounded ${isActive ? 'bg-red-700 font-semibold' : 'hover:bg-red-700'}`
                } onClick={() => setIsOpen(false)}>📤 Deployments</NavLink>

                <NavLink to="/daemonsets" className={({ isActive }) =>
                  `p-2 rounded ${isActive ? 'bg-red-700 font-semibold' : 'hover:bg-red-700'}`
                } onClick={() => setIsOpen(false)}>🔁 Daemon Sets</NavLink>

                <NavLink to="/statefulsets" className={({ isActive }) =>
                  `p-2 rounded ${isActive ? 'bg-red-700 font-semibold' : 'hover:bg-red-700'}`
                } onClick={() => setIsOpen(false)}>💾 Stateful Sets</NavLink>

                <NavLink to="/replicasets" className={({ isActive }) =>
                  `p-2 rounded ${isActive ? 'bg-red-700 font-semibold' : 'hover:bg-red-700'}`
                } onClick={() => setIsOpen(false)}>📑 Replica Sets</NavLink>

                <NavLink to="/replicationcontrollers" className={({ isActive }) =>
                  `p-2 rounded ${isActive ? 'bg-red-700 font-semibold' : 'hover:bg-red-700'}`
                } onClick={() => setIsOpen(false)}>📋 Replication Controllers</NavLink>

                <NavLink to="/jobs" className={({ isActive }) =>
                  `p-2 rounded ${isActive ? 'bg-red-700 font-semibold' : 'hover:bg-red-700'}`
                } onClick={() => setIsOpen(false)}>🛠️ Jobs</NavLink>

                <NavLink to="/cronjobs" className={({ isActive }) =>
                  `p-2 rounded ${isActive ? 'bg-red-700 font-semibold' : 'hover:bg-red-700'}`
                } onClick={() => setIsOpen(false)}>⏰ Cron Jobs</NavLink>
              </div>
            )}
          </div>

          {/* Config Section */}
          <div>
            <button
              className="flex items-center justify-between w-full p-2 rounded hover:bg-red-700"
              onClick={() => setShowConfig(!showConfig)}
            >
              <span>🛠️ Config</span>
              {showConfig ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
            </button>

            {showConfig && (
              <div className="ml-4 mt-2 flex flex-col space-y-2">
                <NavLink to="/configmaps" className={({ isActive }) =>
                  `p-2 rounded ${isActive ? 'bg-red-700 font-semibold' : 'hover:bg-red-700'}`
                } onClick={() => setIsOpen(false)}>🧾 Config Maps</NavLink>

                <NavLink to="/secrets" className={({ isActive }) =>
                  `p-2 rounded ${isActive ? 'bg-red-700 font-semibold' : 'hover:bg-red-700'}`
                } onClick={() => setIsOpen(false)}>🔐 Secrets</NavLink>

                <NavLink to="/resourcequotas" className={({ isActive }) =>
                  `p-2 rounded ${isActive ? 'bg-red-700 font-semibold' : 'hover:bg-red-700'}`
                } onClick={() => setIsOpen(false)}>🎯 Resource Quotas</NavLink>

                <NavLink to="/limitranges" className={({ isActive }) =>
                  `p-2 rounded ${isActive ? 'bg-red-700 font-semibold' : 'hover:bg-red-700'}`
                } onClick={() => setIsOpen(false)}>📜 Limit Ranges </NavLink>

                <NavLink to="/horizontalpodautoscalers" className={({ isActive }) =>
                  `p-2 rounded ${isActive ? 'bg-red-700 font-semibold' : 'hover:bg-red-700'}`
                } onClick={() => setIsOpen(false)}>📈 Horizontal Pod </NavLink>
              </div>
            )}
          </div>

          {/* Network Section */}
          <div>
            <button
              className="flex items-center justify-between w-full p-2 rounded hover:bg-red-700"
              onClick={() => setShowNetwork(!showNetwork)}
            >
              <span>🌐 Network</span>
              {showNetwork ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
            </button>

            {showNetwork && (
              <div className="ml-4 mt-2 flex flex-col space-y-2">
                <NavLink to="/services" className={({ isActive }) =>
                  `p-2 rounded ${isActive ? 'bg-red-700 font-semibold' : 'hover:bg-red-700'}`
                } onClick={() => setIsOpen(false)}>🌍 Services </NavLink>
                <NavLink to="/endpoints" className={({ isActive }) =>
                  `p-2 rounded ${isActive ? 'bg-red-700 font-semibold' : 'hover:bg-red-700'}`
                } onClick={() => setIsOpen(false)}>📡 Endpoints </NavLink>
                <NavLink to="/ingresses" className={({ isActive }) =>
                  `p-2 rounded ${isActive ? 'bg-red-700 font-semibold' : 'hover:bg-red-700'}`
                } onClick={() => setIsOpen(false)}>🚪 Ingresses  </NavLink>
                <NavLink to="/ingressclasses" className={({ isActive }) =>
                  `p-2 rounded ${isActive ? 'bg-red-700 font-semibold' : 'hover:bg-red-700'}`
                } onClick={() => setIsOpen(false)}>🏷️ Ingress Classes  </NavLink>
                <NavLink to="/networkpolicies" className={({ isActive }) =>
                  `p-2 rounded ${isActive ? 'bg-red-700 font-semibold' : 'hover:bg-red-700'}`
                } onClick={() => setIsOpen(false)}>🔗 Network Policies  </NavLink>
              </div>
            )}
          </div>

          {/* Storage Section */}
          <div>
            <button
              className="flex items-center justify-between w-full p-2 rounded hover:bg-red-700"
              onClick={() => setShowStorage(!showStorage)}
            >
              <span>🗄️ Storage</span>
              {showStorage ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
            </button>

            {showStorage && (
              <div className="ml-4 mt-2 flex flex-col space-y-2">
                <NavLink to="/persistentvolumeclaims" className={({ isActive }) =>
                  `p-2 rounded ${isActive ? 'bg-red-700 font-semibold' : 'hover:bg-red-700'}`
                } onClick={() => setIsOpen(false)}>📂 Persistent Volume Claims </NavLink>
                <NavLink to="/persistentvolumes" className={({ isActive }) =>
                  `p-2 rounded ${isActive ? 'bg-red-700 font-semibold' : 'hover:bg-red-700'}`
                } onClick={() => setIsOpen(false)}>📦 Persistent Volumes </NavLink>
                <NavLink to="/storageclasses" className={({ isActive }) =>
                  `p-2 rounded ${isActive ? 'bg-red-700 font-semibold' : 'hover:bg-red-700'}`
                } onClick={() => setIsOpen(false)}>📚 Storage Classes </NavLink>
              </div>
            )}
          </div>

          {/* Helm Section */}
          <div>
            <button
              className="flex items-center justify-between w-full p-2 rounded hover:bg-red-700"
              onClick={() => setShowHelm(!showHelm)}
            >
              <span>⎈ Helm</span>
              {showHelm ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
            </button>

            {showHelm && (
              <div className="ml-4 mt-2 flex flex-col space-y-2">
                <NavLink to="/helmcharts" className={({ isActive }) =>
                  `p-2 rounded ${isActive ? 'bg-red-700 font-semibold' : 'hover:bg-red-700'}`
                } onClick={() => setIsOpen(false)}>📊 Charts</NavLink>
                <NavLink to="/helmreleases" className={({ isActive }) =>
                  `p-2 rounded ${isActive ? 'bg-red-700 font-semibold' : 'hover:bg-red-700'}`
                } onClick={() => setIsOpen(false)}>🚀 Releases</NavLink>
              </div>
            )}
          </div>

          {/* Namespaces Section */}
          <NavLink
            to="/namespaces"
            className={({ isActive }) =>
              `p-2 rounded ${isActive ? 'bg-red-700 font-semibold' : 'hover:bg-red-700'}`
            }
            onClick={() => setIsOpen(false)}
          >
            🧭 Namespaces
          </NavLink>

          {/* Events Section */}
          <NavLink
            to="/events"
            className={({ isActive }) =>
              `p-2 rounded ${isActive ? 'bg-red-700 font-semibold' : 'hover:bg-red-700'}`
            }
            onClick={() => setIsOpen(false)}
          >
            📊 Events
          </NavLink>

          {/* Access Control Section */}
          <div>
            <button
              className="flex items-center justify-between w-full p-2 rounded hover:bg-red-700"
              onClick={() => setShowAccessControl(!showAccessControl)}
            >
              <span>🔒 Access Control</span>
              {showAccessControl ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
            </button>

            {showAccessControl && (
              <div className="ml-4 mt-2 flex flex-col space-y-2">
                <NavLink to="/serviceaccounts" className={({ isActive }) =>
                  `p-2 rounded ${isActive ? 'bg-red-700 font-semibold' : 'hover:bg-red-700'}`
                } onClick={() => setIsOpen(false)}>👤 Service Accounts</NavLink>

                <NavLink to="/clusterroles" className={({ isActive }) =>
                  `p-2 rounded ${isActive ? 'bg-red-700 font-semibold' : 'hover:bg-red-700'}`
                } onClick={() => setIsOpen(false)}>🌐 Cluster Roles</NavLink>

                <NavLink to="/roles" className={({ isActive }) =>
                  `p-2 rounded ${isActive ? 'bg-red-700 font-semibold' : 'hover:bg-red-700'}`
                } onClick={() => setIsOpen(false)}>📝 Roles</NavLink>

                <NavLink to="/clusterrolebindings" className={({ isActive }) =>
                  `p-2 rounded ${isActive ? 'bg-red-700 font-semibold' : 'hover:bg-red-700'}`
                } onClick={() => setIsOpen(false)}>🔗 Cluster Role Bindings</NavLink>

                <NavLink to="/rolebindings" className={({ isActive }) =>
                  `p-2 rounded ${isActive ? 'bg-red-700 font-semibold' : 'hover:bg-red-700'}`
                } onClick={() => setIsOpen(false)}>🔗 Role Bindings</NavLink>
              </div>
            )}
          </div>

          {/* Custom Resources Section */}
          <div>
            <button
              className="flex items-center justify-between w-full p-2 rounded hover:bg-red-700"
              onClick={() => setShowCustomResources(!showCustomResources)}
            >
              <span>🧩 Custom Resources</span>
              {showCustomResources ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
            </button>

            {showCustomResources && (
              <div className="ml-4 mt-2 flex flex-col space-y-2">
                <NavLink to="/definitions" className={({ isActive }) =>
                  `p-2 rounded ${isActive ? 'bg-red-700 font-semibold' : 'hover:bg-red-700'}`
                } onClick={() => setIsOpen(false)}>📋 Definitions</NavLink>

                {/* ACME Resources */}
                <div>
                  <button
                    className="flex items-center justify-between w-full p-2 rounded hover:bg-red-700"
                    onClick={() => setShowACME(!showACME)}
                  >
                    <span>🔐 acme.cert-manager.io</span>
                    {showACME ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
                  </button>

                  {showACME && (
                    <div className="ml-4 mt-2 flex flex-col space-y-2">
                      <NavLink to="/challenges" className={({ isActive }) =>
                        `p-2 rounded ${isActive ? 'bg-red-700 font-semibold' : 'hover:bg-red-700'}`
                      } onClick={() => setIsOpen(false)}>🔍 Challenge</NavLink>

                      <NavLink to="/orders" className={({ isActive }) =>
                        `p-2 rounded ${isActive ? 'bg-red-700 font-semibold' : 'hover:bg-red-700'}`
                      } onClick={() => setIsOpen(false)}>📝 Order</NavLink>
                    </div>
                  )}
                </div>

                {/* Cert Manager Resources */}
                <div>
                  <button
                    className="flex items-center justify-between w-full p-2 rounded hover:bg-red-700"
                    onClick={() => setShowCertManager(!showCertManager)}
                  >
                    <span>🔏 cert-manager.io</span>
                    {showCertManager ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
                  </button>

                  {showCertManager && (
                    <div className="ml-4 mt-2 flex flex-col space-y-2">
                      <NavLink to="/certificates" className={({ isActive }) =>
                        `p-2 rounded ${isActive ? 'bg-red-700 font-semibold' : 'hover:bg-red-700'}`
                      } onClick={() => setIsOpen(false)}>📜 Certificate</NavLink>

                      <NavLink to="/certificaterequests" className={({ isActive }) =>
                        `p-2 rounded ${isActive ? 'bg-red-700 font-semibold' : 'hover:bg-red-700'}`
                      } onClick={() => setIsOpen(false)}>📩 Certificate Request</NavLink>

                      <NavLink to="/issuers" className={({ isActive }) =>
                        `p-2 rounded ${isActive ? 'bg-red-700 font-semibold' : 'hover:bg-red-700'}`
                      } onClick={() => setIsOpen(false)}>🏛️ Issuer</NavLink>

                      <NavLink to="/clusterissuers" className={({ isActive }) =>
                        `p-2 rounded ${isActive ? 'bg-red-700 font-semibold' : 'hover:bg-red-700'}`
                      } onClick={() => setIsOpen(false)}>🌐 Cluster Issuer</NavLink>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </nav>
      </div>
    </>
  );
};

export default Sidebar;
