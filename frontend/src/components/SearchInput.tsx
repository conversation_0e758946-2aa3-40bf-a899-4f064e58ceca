import React from 'react';
import { TextField } from '@mui/material';

interface SearchInputProps {
  value: string;
  onChange: (value: string) => void;
}

const SearchInput: React.FC<SearchInputProps> = ({ value, onChange }) => {
  return (
    <TextField
      label="Search"
      variant="outlined"
      size="small"
      value={value}
      onChange={(e) => onChange(e.target.value)}
      sx={{ minWidth: 250 }} 
    />
  );
};

export default SearchInput;
