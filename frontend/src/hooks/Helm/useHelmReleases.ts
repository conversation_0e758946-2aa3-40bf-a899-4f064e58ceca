import { useEffect, useState } from 'react';
import { getHelmReleases } from '../../services/api/api';
import { useLoading } from '../../contexts/LoadingContext';

interface HelmRelease {
  name: string;
  namespace: string;
  chart: string;
  revision: number;
  version: string;
  app_version: string;
  status: string;
  updated: string;
}

export const useHelmReleases = () => {
  const { startLoading, stopLoading } = useLoading();
  const [releases, setReleases] = useState<HelmRelease[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchHelmReleases = async () => {
      startLoading();
      try {
        const data = await getHelmReleases();
        setReleases(data.items ?? []);
      } catch (err) {
        setError('Failed to fetch Helm Releases');
      } finally {
        stopLoading();
        setLoading(false);
      }
    };

    fetchHelmReleases();
  }, []);

  return { releases, loading, error };
};
