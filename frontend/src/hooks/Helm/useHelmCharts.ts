import { useEffect, useState } from 'react';
import { getHelmCharts } from '../../services/api/api';
import { useLoading } from '../../contexts/LoadingContext';

interface HelmChart {
  name: string;
  description: string;
  version: string;
  appVersion: string;
  repository: string;
}

export const useHelmCharts = () => {
  const { startLoading, stopLoading } = useLoading();
  const [charts, setCharts] = useState<HelmChart[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchHelmCharts = async () => {
      startLoading();
      try {
        const data = await getHelmCharts();
        setCharts(data.items ?? []);
      } catch (err) {
        setError('Failed to fetch Helm Charts');
      } finally {
        stopLoading();
        setLoading(false);
      }
    };

    fetchHelmCharts();
  }, []);

  return { charts, loading, error };
};
