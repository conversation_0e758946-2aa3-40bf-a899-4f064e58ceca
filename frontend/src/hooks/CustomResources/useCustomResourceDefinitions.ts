import { useEffect, useState } from 'react';
import { getCustomResourceDefinitions } from '../../services/api/api';
import { useLoading } from '../../contexts/LoadingContext';

interface CustomResourceDefinition {
  metadata: {
    name: string;
    creationTimestamp: string;
  };
  spec: {
    group: string;
    names: {
      kind: string;
      plural: string;
      singular: string;
    };
    scope: string;
    versions: Array<{
      name: string;
      served: boolean;
      storage: boolean;
    }>;
  };
}

export const useCustomResourceDefinitions = () => {
  const { startLoading, stopLoading } = useLoading();
  const [crds, setCrds] = useState<CustomResourceDefinition[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCRDs = async () => {
      startLoading();
      try {
        const data = await getCustomResourceDefinitions();
        setCrds(data.items ?? []);
      } catch (err) {
        setError('Failed to fetch Custom Resource Definitions');
      } finally {
        stopLoading();
        setLoading(false);
      }
    };

    fetchCRDs();
  }, []);

  return { crds, loading, error };
};
