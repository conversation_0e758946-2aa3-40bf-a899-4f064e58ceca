import { useEffect, useState } from 'react';
import { getCertificateRequests } from '../../../services/api/api';
import { useLoading } from '../../../contexts/LoadingContext';

interface CertificateRequest {
  metadata: {
    name: string;
    namespace: string;
    creationTimestamp: string;
  };
  spec: {
    request: string;
    issuerRef: {
      name: string;
      kind: string;
      group: string;
    };
    duration?: string;
    username?: string;
    groups?: string[];
    uid?: string;
  };
  status: {
    conditions?: Array<{
      type: string;
      status: string;
      reason: string;
      message: string;
      lastTransitionTime: string;
    }>;
    certificate?: string;
    ca?: string;
    failureTime?: string;
  };
}

export const useCertificateRequests = () => {
  const { startLoading, stopLoading } = useLoading();
  const [certificateRequests, setCertificateRequests] = useState<CertificateRequest[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCertificateRequests = async () => {
      startLoading();
      try {
        const data = await getCertificateRequests();
        setCertificateRequests(data.items ?? []);
      } catch (err) {
        setError('Failed to fetch Certificate Requests');
      } finally {
        stopLoading();
        setLoading(false);
      }
    };

    fetchCertificateRequests();
  }, []);

  return { certificateRequests, loading, error };
};
