import { useEffect, useState } from 'react';
import { getCertificates } from '../../../services/api/api';
import { useLoading } from '../../../contexts/LoadingContext';

interface Certificate {
  metadata: {
    name: string;
    namespace: string;
    creationTimestamp: string;
  };
  spec: {
    secretName: string;
    issuerRef: {
      name: string;
      kind: string;
      group: string;
    };
    commonName?: string;
    dnsNames?: string[];
    duration?: string;
    renewBefore?: string;
  };
  status: {
    conditions?: Array<{
      type: string;
      status: string;
      reason: string;
      message: string;
      lastTransitionTime: string;
    }>;
    notAfter?: string;
    notBefore?: string;
    renewalTime?: string;
    revision?: number;
  };
}

export const useCertificates = () => {
  const { startLoading, stopLoading } = useLoading();
  const [certificates, setCertificates] = useState<Certificate[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCertificates = async () => {
      startLoading();
      try {
        const data = await getCertificates();
        setCertificates(data.items ?? []);
      } catch (err) {
        setError('Failed to fetch Certificates');
      } finally {
        stopLoading();
        setLoading(false);
      }
    };

    fetchCertificates();
  }, []);

  return { certificates, loading, error };
};
