import { useEffect, useState } from 'react';
import { getClusterIssuers } from '../../../services/api/api';
import { useLoading } from '../../../contexts/LoadingContext';

interface ClusterIssuer {
  metadata: {
    name: string;
    creationTimestamp: string;
  };
  spec: {
    acme?: {
      server: string;
      email: string;
      privateKeySecretRef: {
        name: string;
      };
      solvers?: Array<{
        http01?: {
          ingress?: {
            class?: string;
            name?: string;
          };
        };
        dns01?: {
          provider: string;
        };
      }>;
    };
    ca?: {
      secretName: string;
    };
    selfSigned?: {
      crlDistributionPoints?: string[];
    };
    vault?: {
      path: string;
      server: string;
      caBundle?: string;
    };
    venafi?: {
      zone: string;
      cloud?: {
        apiTokenSecretRef: {
          name: string;
          key: string;
        };
      };
      tpp?: {
        url: string;
        caBundle?: string;
        credentialsRef: {
          name: string;
        };
      };
    };
  };
  status: {
    conditions?: Array<{
      type: string;
      status: string;
      reason: string;
      message: string;
      lastTransitionTime: string;
    }>;
    acme?: {
      uri: string;
    };
  };
}

export const useClusterIssuers = () => {
  const { startLoading, stopLoading } = useLoading();
  const [clusterIssuers, setClusterIssuers] = useState<ClusterIssuer[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchClusterIssuers = async () => {
      startLoading();
      try {
        const data = await getClusterIssuers();
        setClusterIssuers(data.items ?? []);
      } catch (err) {
        setError('Failed to fetch Cluster Issuers');
      } finally {
        stopLoading();
        setLoading(false);
      }
    };

    fetchClusterIssuers();
  }, []);

  return { clusterIssuers, loading, error };
};
