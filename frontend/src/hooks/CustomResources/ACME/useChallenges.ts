import { useEffect, useState } from 'react';
import { getChallenges } from '../../../services/api/api';
import { useLoading } from '../../../contexts/LoadingContext';

interface Challenge {
  metadata: {
    name: string;
    namespace: string;
    creationTimestamp: string;
  };
  spec: {
    type: string;
    url: string;
    dnsName: string;
    token: string;
    key: string;
    wildcard: boolean;
    solver: {
      http01?: {
        ingress?: {
          name?: string;
          class?: string;
          serviceType?: string;
        };
      };
      dns01?: {
        provider: string;
      };
    };
  };
  status: {
    state: string;
    reason: string;
    processing: boolean;
    presented: boolean;
  };
}

export const useChallenges = () => {
  const { startLoading, stopLoading } = useLoading();
  const [challenges, setChallenges] = useState<Challenge[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchChallenges = async () => {
      startLoading();
      try {
        const data = await getChallenges();
        setChallenges(data.items ?? []);
      } catch (err) {
        setError('Failed to fetch ACME Challenges');
      } finally {
        stopLoading();
        setLoading(false);
      }
    };

    fetchChallenges();
  }, []);

  return { challenges, loading, error };
};
