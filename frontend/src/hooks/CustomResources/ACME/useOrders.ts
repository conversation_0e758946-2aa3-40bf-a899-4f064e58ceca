import { useEffect, useState } from 'react';
import { getOrders } from '../../../services/api/api';
import { useLoading } from '../../../contexts/LoadingContext';

interface Order {
  metadata: {
    name: string;
    namespace: string;
    creationTimestamp: string;
  };
  spec: {
    request: string;
    issuerRef: {
      name: string;
      kind: string;
      group: string;
    };
    commonName?: string;
    dnsNames?: string[];
  };
  status: {
    state: string;
    reason?: string;
    failureTime?: string;
    url?: string;
    finalizeURL?: string;
    certificate?: string;
  };
}

export const useOrders = () => {
  const { startLoading, stopLoading } = useLoading();
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchOrders = async () => {
      startLoading();
      try {
        const data = await getOrders();
        setOrders(data.items ?? []);
      } catch (err) {
        setError('Failed to fetch ACME Orders');
      } finally {
        stopLoading();
        setLoading(false);
      }
    };

    fetchOrders();
  }, []);

  return { orders, loading, error };
};
