import { useEffect, useState } from 'react';
import { getNodes } from '../../services/api/api';
import { useLoading } from '../../contexts/LoadingContext'; // Import the context

interface Node {
  metadata: {
    name: string;
    namespace?: string;
    creationTimestamp: string;
    labels?: { [key: string]: string };
  };
  spec?: {
    taints?: Array<any>;
  };
  status?: {
    conditions?: Array<{
      type: string;
      status: string;
    }>;
    nodeInfo?: {
      kubeletVersion?: string;
    };
  };
}

export const useNodes = () => {
  const { startLoading, stopLoading } = useLoading(); // Access loading context
  const [nodes, setNodes] = useState<Node[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchNodes = async () => {
      startLoading(); // Start the global loading bar
      try {
        const data = await getNodes();
        setNodes(data.items ?? []);
      } catch (err) {
        setError('Failed to fetch nodes');
      } finally {
        stopLoading(); // Stop the global loading bar
        setLoading(false);
      }
    };

    fetchNodes();
  }, []); // Ensure that context functions are included in dependencies

  return { nodes, loading, error };
};
