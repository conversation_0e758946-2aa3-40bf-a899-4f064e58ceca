import { useEffect, useState } from 'react';
import { getSecrets} from '../../services/api/api';
import { useLoading } from '../../contexts/LoadingContext';


interface ConfigMaps {
  metadata: {
    name: string;
    creationTimestamp: string;
    namespace: string;
    labels?: { [key: string]: string };
  };
  data?: Record<string, string>;
}

export const useSecrets = () => {
  const { startLoading, stopLoading } = useLoading();
  const [secrets, setSecrets] = useState<ConfigMaps[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSecrets = async () => {
      startLoading();
      try {
        const data = await getSecrets();
        setSecrets(data.items ?? []);
      } catch (err) {
        setError('Failed to fetch Secrets');
      } finally {
        stopLoading();
        setLoading(false);
      }
    };

    fetchSecrets();
  }, []);

  return { secrets, loading, error };
};