import { useEffect, useState } from 'react';
import { getResourceQuotas } from '../../services/api/api';
import { useLoading } from '../../contexts/LoadingContext';

interface ResourceQuota {
  metadata: {
    name: string;
    creationTimestamp: string;
    namespace: string;
  };
}

export const useResourceQuotas = (namespaces: string[]) => {
  const { startLoading, stopLoading } = useLoading();
  const [resourceQuotas, setResourceQuotas] = useState<ResourceQuota[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!namespaces || namespaces.length === 0) return;

    const fetchAllQuotas = async () => {
      startLoading();
      setLoading(true);
      setError(null);

      try {
        const requests = namespaces.map(ns => getResourceQuotas(ns));
        const results = await Promise.all(requests);

        // Flatten all quotas from all namespaces
        const allQuotas = results.flatMap(result => result.items ?? []);
        setResourceQuotas(allQuotas);
      } catch (err) {
        setError('Failed to fetch Resource quotas');
      } finally {
        stopLoading();
        setLoading(false);
      }
    };

    fetchAllQuotas();
  }, []);

  return { resourceQuotas, loading, error };
};
