import { useEffect, useState } from 'react';
import { getHorizontalPodAutoscalers} from '../../services/api/api';
import { useLoading } from '../../contexts/LoadingContext';


interface HorizontalPodAutoscalers {
    metadata: {
      name: string;
      creationTimestamp: string;
      namespace: string;
    };
    spec: {
      minReplicas?: number;
      maxReplicas: number;
    };
    status: {
      currentReplicas: number;
      desiredReplicas: number;
      currentCPUUtilizationPercentage?: number;
    };
  }
  

export const useHorizontalPods = () => {
  const { startLoading, stopLoading } = useLoading();
  const [horizontalPod, setHorizontalPod] = useState<HorizontalPodAutoscalers[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchHorizontalPod = async () => {
      startLoading();
      try {
        const data = await getHorizontalPodAutoscalers();
        setHorizontalPod(data.items ?? []);
      } catch (err) {
        setError('Failed to fetch Horizontal Pod Autoscalers');
      } finally {
        stopLoading();
        setLoading(false);
      }
    };

    fetchHorizontalPod();
  }, []);

  return { horizontalPod, loading, error };
};