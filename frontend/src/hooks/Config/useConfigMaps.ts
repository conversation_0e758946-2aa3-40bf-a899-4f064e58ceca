import { useEffect, useState } from 'react';
import { getConfigMaps} from '../../services/api/api';
import { useLoading } from '../../contexts/LoadingContext';


interface ConfigMaps {
  metadata: {
    name: string;
    creationTimestamp: string;
    namespace: string;
  };
  data?: Record<string, string>;
}

export const useConfigMaps = () => {
  const { startLoading, stopLoading } = useLoading();
  const [configMaps, setConfigMaps] = useState<ConfigMaps[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchConfigMaps = async () => {
      startLoading();
      try {
        const data = await getConfigMaps();
        setConfigMaps(data.items ?? []);
      } catch (err) {
        setError('Failed to fetch Config Maps');
      } finally {
        stopLoading();
        setLoading(false);
      }
    };

    fetchConfigMaps();
  }, []);

  return { configMaps, loading, error };
};