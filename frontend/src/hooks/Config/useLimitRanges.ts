import { useEffect, useState } from 'react';
import { getLimitRanges} from '../../services/api/api';
import { useLoading } from '../../contexts/LoadingContext';


interface LimitRanges {
  metadata: {
    name: string;
    creationTimestamp: string;
    namespace: string;
  };
}

export const useLimitRanges = () => {
  const { startLoading, stopLoading } = useLoading();
  const [limitRanges, setLimitRanges] = useState<LimitRanges[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchLimitRanges = async () => {
      startLoading();
      try {
        const data = await getLimitRanges();
        setLimitRanges(data.items ?? []);
      } catch (err) {
        setError('Failed to fetch Limit Ranges');
      } finally {
        stopLoading();
        setLoading(false);
      }
    };

    fetchLimitRanges();
  }, []);

  return { limitRanges, loading, error };
};