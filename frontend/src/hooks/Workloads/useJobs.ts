import { useEffect, useState } from 'react';
import { getJobs } from '../../services/api/api';
import { useLoading } from '../../contexts/LoadingContext';

interface JobCondition {
  type: string;
  status: string;
  lastProbeTime?: string;
  lastTransitionTime?: string;
  reason?: string;
  message?: string;
}

interface JobStatus {
  completionTime?: string;
  startTime?: string;
  active?: number;
  succeeded?: number;
  failed?: number;
  conditions?: JobCondition[];
}

interface Job {
  metadata: {
    name: string;
    creationTimestamp: string;
    namespace: string;
  
  };
  spec?: {
    completions?: number;
    parallelism?: number;
    backoffLimit?: number;
  };
  status?: JobStatus;
}

export const useJobs = () => {
  const { startLoading, stopLoading } = useLoading();
  const [jobs, setJobs] = useState<Job[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchJobs = async () => {
      startLoading();
      try {
        const data = await getJobs();
        setJobs(data.items ?? []);
      } catch (err) {
        setError('Failed to fetch Jobs');
      } finally {
        stopLoading();
        setLoading(false);
      }
    };

    fetchJobs();
  }, []);

  return { jobs, loading, error };
};