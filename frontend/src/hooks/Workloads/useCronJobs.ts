import { useEffect, useState } from 'react';
import { getCronJobs } from '../../services/api/api';
import { useLoading } from '../../contexts/LoadingContext';

interface CronJobMetadata {
  name: string;
  creationTimestamp: string;
  namespace: string;
}

interface CronJobSpec {
  schedule: string;
  timeZone?: string; // Optional as it was added in Kubernetes 1.24
  // Add other spec fields as needed
}

interface CronJobStatus {
  active?: Array<{
    apiVersion?: string;
    kind?: string;
    name?: string;
    namespace?: string;
    uid?: string;
  }>;
  lastScheduleTime?: string;
  lastSuccessfulTime?: string;
  nextScheduleTime?: string;
}

interface CronJob {
  metadata: CronJobMetadata;
  spec: CronJobSpec;
  status?: CronJobStatus; // Optional as it might not always be present
}

export const useCronJobs = () => {
  const { startLoading, stopLoading } = useLoading();
  const [cronJobs, setCronJobs] = useState<CronJob[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCronJobs = async () => {
      startLoading();
      try {
        const data = await getCronJobs();
        setCronJobs(data.items ?? []);
      } catch (err) {
        setError('Failed to fetch cron jobs');
      } finally {
        stopLoading();
        setLoading(false);
      }
    };

    fetchCronJobs();
  }, []);

  return { cronJobs, loading, error };
};