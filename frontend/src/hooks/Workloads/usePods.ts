import { useEffect, useState } from 'react';
import { getPods } from '../../services/api/api';
import { useLoading } from '../../contexts/LoadingContext';
interface OwnerReference {
  apiVersion: string;
  kind: string;
  name: string;
  uid: string;
  controller?: boolean;
  blockOwnerDeletion?: boolean;
}

interface ContainerState {
  running?: {
    startedAt: string;
  };
  terminated?: {
    exitCode: number;
    reason: string;
    startedAt: string;
    finishedAt: string;
  };
  waiting?: {
    reason: string;
    message?: string;
  };
}

interface ContainerStatus {
  name: string;
  state: ContainerState;
  lastState: ContainerState;
  ready: boolean;
  restartCount: number;
  image: string;
  imageID: string;
  containerID: string;
  started?: boolean;
}

interface Conditions {
  type: string;
  status: string;
}

interface Pod {
  metadata: {
    name: string;
    creationTimestamp: string;
    namespace: string;
    ownerReferences?: OwnerReference[];
  };
  spec?: {
    nodeName?: string;
  };
  status?: {
    phase?: string;
    qosClass?: string;
    conditions?: Conditions[];
    containerStatuses?: ContainerStatus[];
    initContainerStatuses?: ContainerStatus[];
  };
}

export const usePods = () => {
  const { startLoading, stopLoading } = useLoading(); // Access loading context
  const [pods, setPods] = useState<Pod[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPods = async () => {
      startLoading(); // Start the global loading bar
      try {
        const data = await getPods();
        setPods(data.items ?? []);
      } catch (err) {
        setError('Failed to fetch pods');
      } finally {
        stopLoading(); // Stop the global loading bar
        setLoading(false);
      }
    };

    fetchPods();
  }, []);

  return { pods, loading, error };
};
