import { useEffect, useState } from 'react';
import { getReplicationControllers } from '../../services/api/api';
import { useLoading } from '../../contexts/LoadingContext';

interface ReplicationController {
  metadata: {
    name: string;
    creationTimestamp: string;
    namespace: string;
  };
  spec: {
    replicas: number; // Desired Replicas
    selector: {
      [key: string]: string; // Selector (key-value pairs)
    };
  };
  status: {
    replicas: number; // Current Replicas
    availableReplicas?: number;
    readyReplicas?: number;
  };
}

export const useReplicationControllers = () => {
  const { startLoading, stopLoading } = useLoading();
  const [replicationCont, setReplicationCont] = useState<ReplicationController[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchReplicationControllers = async () => {
      startLoading();
      try {
        const data = await getReplicationControllers();
        setReplicationCont(data.items ?? []);
      } catch (err) {
        setError('Failed to fetch replication controllers');
      } finally {
        stopLoading();
        setLoading(false);
      }
    };

    fetchReplicationControllers();
  }, []);

  return { replicationCont, loading, error };
};