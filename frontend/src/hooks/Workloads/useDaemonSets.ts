import { useEffect, useState } from 'react';
import { getDaemonSets } from '../../services/api/api';
import { useLoading } from '../../contexts/LoadingContext'; // Import the context

interface DaemonSets {
  metadata: {
    name: string;
    creationTimestamp: string;
    namespace: string;
  };
  spec?: {
    template: {
      spec: {
        nodeSelector?: Record<string, string>;
      };
    };
  };
  status?: {
    currentNumberScheduled: number;
    desiredNumberScheduled: number;
    numberAvailable: number;
    numberMisscheduled: number;
    numberReady: number;
    observedGeneration: number;
    updatedNumberScheduled: number;
  };
}

export const useDaemonSets = () => {
  const { startLoading, stopLoading } = useLoading(); // Access loading context
  const [daemonSets, setDaemonSets] = useState<DaemonSets[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDeployments = async () => {
      startLoading(); // Start the global loading bar
      try {
        const data = await getDaemonSets();
        setDaemonSets(data.items ?? []);
      } catch (err) {
        setError('Failed to fetch daemonsets');
      } finally {
        stopLoading(); // Stop the global loading bar
        setLoading(false);
      }
    };

    fetchDeployments();
  }, []);

  return { daemonSets, loading, error };
};
