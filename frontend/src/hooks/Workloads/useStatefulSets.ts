import { useEffect, useState } from 'react';
import { getStatefulSets } from '../../services/api/api';
import { useLoading } from '../../contexts/LoadingContext'; // Import the context

interface StatefulSets {
  metadata: {
    name: string;
    creationTimestamp: string;
    namespace: string;
  };
 
  status?: {
    replicas: number;
    availableReplicas: number;
    unavailableReplicas: number;
  };
}

export const useStatefulSets = () => {
  const { startLoading, stopLoading } = useLoading(); // Access loading context
  const [statefulSets, setStatefulSets] = useState<StatefulSets[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStatefulSets = async () => {
      startLoading(); // Start the global loading bar
      try {
        const data = await getStatefulSets();
        setStatefulSets(data.items ?? []);
      } catch (err) {
        setError('Failed to fetch StatefulSets');
      } finally {
        stopLoading(); // Stop the global loading bar
        setLoading(false);
      }
    };

    fetchStatefulSets();
  }, []);

  return { statefulSets, loading, error };
};
