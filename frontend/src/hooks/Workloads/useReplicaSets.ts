import { useEffect, useState } from 'react';
import { getReplicaSets } from '../../services/api/api';
import { useLoading } from '../../contexts/LoadingContext'; // Import the context

interface ReplicaSets {
    metadata: {
      name: string;
      creationTimestamp: string;
      namespace: string;
      annotations?: {
        "deployment.kubernetes.io/desired-replicas": string;
        "deployment.kubernetes.io/max-replicas": string;
      };
    };
    spec: {
      replicas: number; // "desired" replicas (from .spec.replicas)
    };
    status?: {
      replicas: number; // "current" replicas (total pods)
      readyReplicas?: number; // "ready" replicas (pods passing readiness checks)
      availableReplicas?: number; // Optional: Available replicas
    };
  }

export const useReplicaSets = () => {
  const { startLoading, stopLoading } = useLoading(); // Access loading context
  const [replicas, setReplicas] = useState<ReplicaSets[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchReplicaSets = async () => {
      startLoading(); // Start the global loading bar
      try {
        const data = await getReplicaSets();
        setReplicas(data.items ?? []);
      } catch (err) {
        setError('Failed to fetch replicasets');
      } finally {
        stopLoading(); // Stop the global loading bar
        setLoading(false);
      }
    };

    fetchReplicaSets();
  }, []);

  return { replicas, loading, error };
};
