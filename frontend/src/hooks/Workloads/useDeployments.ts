import { useEffect, useState } from 'react';
import { getDeployments } from '../../services/api/api';
import { useLoading } from '../../contexts/LoadingContext'; // Import the context

interface Deployments {
  metadata: {
    name: string;
    creationTimestamp: string;
    namespace: string;
  };
  status?: {
    replicas: number;
    availableReplicas: number;
    unavailableReplicas: number;
  };
}

export const useDeployments = () => {
  const { startLoading, stopLoading } = useLoading(); // Access loading context
  const [deployments, setDeployments] = useState<Deployments[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDeployments = async () => {
      startLoading(); // Start the global loading bar
      try {
        const data = await getDeployments();
        setDeployments(data.items ?? []);
      } catch (err) {
        setError('Failed to fetch deployments');
      } finally {
        stopLoading(); // Stop the global loading bar
        setLoading(false);
      }
    };

    fetchDeployments();
  }, []);

  return { deployments, loading, error };
};
