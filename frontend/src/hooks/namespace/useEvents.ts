import { useEffect, useState } from 'react';
import { getEvents } from '../../services/api/api';
import { useLoading } from '../../contexts/LoadingContext';

interface EventMetadata {
  name: string;
  creationTimestamp: string;
  namespace: string;
  uid: string;
}

interface EventSource {
  component: string;
  host?: string;
}

interface EventInvolvedObject {
  kind: string;
  namespace: string;
  name: string;
  uid: string;
  apiVersion: string;
  resourceVersion?: string;
  fieldPath?: string;
}

interface Event {
  metadata: EventMetadata;
  involvedObject: EventInvolvedObject;
  reason: string;
  message: string;
  source: EventSource;
  firstTimestamp: string;
  lastTimestamp: string;
  count: number;
  type: string;
  eventTime?: string;
  reportingComponent?: string;
  reportingInstance?: string;
  action?: string;
  related?: any;
}

export const useEvents = () => {
  const { startLoading, stopLoading } = useLoading();
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchEvents = async () => {
      startLoading();
      try {
        const data = await getEvents();
        setEvents(data.items ?? []);
      } catch (err) {
        setError('Failed to fetch events');
      } finally {
        stopLoading();
        setLoading(false);
      }
    };

    fetchEvents();
  }, []);

  return { events, loading, error };
};
