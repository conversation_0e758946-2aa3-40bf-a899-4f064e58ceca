import { useEffect, useState } from 'react';
import { getClusterRoles } from '../../services/api/api';
import { useLoading } from '../../contexts/LoadingContext';

interface ClusterRole {
  metadata: {
    name: string;
    creationTimestamp: string;
  };
  rules?: Array<{
    apiGroups: string[];
    resources: string[];
    verbs: string[];
  }>;
}

export const useClusterRoles = () => {
  const { startLoading, stopLoading } = useLoading();
  const [clusterRoles, setClusterRoles] = useState<ClusterRole[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchClusterRoles = async () => {
      startLoading();
      try {
        const data = await getClusterRoles();
        setClusterRoles(data.items ?? []);
      } catch (err) {
        setError('Failed to fetch Cluster Roles');
      } finally {
        stopLoading();
        setLoading(false);
      }
    };

    fetchClusterRoles();
  }, []);

  return { clusterRoles, loading, error };
};
