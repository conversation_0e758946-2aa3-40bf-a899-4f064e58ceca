import { useEffect, useState } from 'react';
import { getClusterRoleBindings } from '../../services/api/api';
import { useLoading } from '../../contexts/LoadingContext';

interface Subject {
  kind: string;
  name: string;
  namespace?: string;
}

interface ClusterRoleBinding {
  metadata: {
    name: string;
    creationTimestamp: string;
  };
  roleRef: {
    apiGroup: string;
    kind: string;
    name: string;
  };
  subjects?: Subject[];
}

export const useClusterRoleBindings = () => {
  const { startLoading, stopLoading } = useLoading();
  const [clusterRoleBindings, setClusterRoleBindings] = useState<ClusterRoleBinding[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchClusterRoleBindings = async () => {
      startLoading();
      try {
        const data = await getClusterRoleBindings();
        setClusterRoleBindings(data.items ?? []);
      } catch (err) {
        setError('Failed to fetch Cluster Role Bindings');
      } finally {
        stopLoading();
        setLoading(false);
      }
    };

    fetchClusterRoleBindings();
  }, []);

  return { clusterRoleBindings, loading, error };
};
