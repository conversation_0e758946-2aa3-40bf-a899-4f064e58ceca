import { useEffect, useState } from 'react';
import { getServiceAccounts } from '../../services/api/api';
import { useLoading } from '../../contexts/LoadingContext';

interface ServiceAccount {
  metadata: {
    name: string;
    namespace: string;
    creationTimestamp: string;
  };
  secrets?: Array<{
    name: string;
  }>;
}

export const useServiceAccounts = () => {
  const { startLoading, stopLoading } = useLoading();
  const [serviceAccounts, setServiceAccounts] = useState<ServiceAccount[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchServiceAccounts = async () => {
      startLoading();
      try {
        const data = await getServiceAccounts();
        setServiceAccounts(data.items ?? []);
      } catch (err) {
        setError('Failed to fetch Service Accounts');
      } finally {
        stopLoading();
        setLoading(false);
      }
    };

    fetchServiceAccounts();
  }, []);

  return { serviceAccounts, loading, error };
};
