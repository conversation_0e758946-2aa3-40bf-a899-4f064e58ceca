import { useEffect, useState } from 'react';
import { getRoles } from '../../services/api/api';
import { useLoading } from '../../contexts/LoadingContext';

interface Role {
  metadata: {
    name: string;
    namespace: string;
    creationTimestamp: string;
  };
  rules?: Array<{
    apiGroups: string[];
    resources: string[];
    verbs: string[];
  }>;
}

export const useRoles = () => {
  const { startLoading, stopLoading } = useLoading();
  const [roles, setRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchRoles = async () => {
      startLoading();
      try {
        const data = await getRoles();
        setRoles(data.items ?? []);
      } catch (err) {
        setError('Failed to fetch Roles');
      } finally {
        stopLoading();
        setLoading(false);
      }
    };

    fetchRoles();
  }, []);

  return { roles, loading, error };
};
