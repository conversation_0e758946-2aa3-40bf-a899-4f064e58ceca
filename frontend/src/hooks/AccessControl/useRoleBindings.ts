import { useEffect, useState } from 'react';
import { getRoleBindings } from '../../services/api/api';
import { useLoading } from '../../contexts/LoadingContext';

interface Subject {
  kind: string;
  name: string;
  namespace?: string;
}

interface RoleBinding {
  metadata: {
    name: string;
    namespace: string;
    creationTimestamp: string;
  };
  roleRef: {
    apiGroup: string;
    kind: string;
    name: string;
  };
  subjects?: Subject[];
}

export const useRoleBindings = () => {
  const { startLoading, stopLoading } = useLoading();
  const [roleBindings, setRoleBindings] = useState<RoleBinding[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchRoleBindings = async () => {
      startLoading();
      try {
        const data = await getRoleBindings();
        setRoleBindings(data.items ?? []);
      } catch (err) {
        setError('Failed to fetch Role Bindings');
      } finally {
        stopLoading();
        setLoading(false);
      }
    };

    fetchRoleBindings();
  }, []);

  return { roleBindings, loading, error };
};
