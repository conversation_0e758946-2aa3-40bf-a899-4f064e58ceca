import { useEffect, useState } from 'react';
import { getPersistentVolumeClaims} from '../../services/api/api';
import { useLoading } from '../../contexts/LoadingContext';


interface persistantVolumeClaims {
  metadata: {
    name: string;
    creationTimestamp: string;
    namespace: string;
  };
  spec: {
    resources: {
      requests: {
        storage: string;
      };
    };
    storageClassName?: string;
  };
  status: {
    phase: string; // Bound, Pending, etc.
  };
}

  

export const usePersistantVolumeClaims = () => {
  const { startLoading, stopLoading } = useLoading();
  const [persistant, setPersistant] = useState<persistantVolumeClaims[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPersistant = async () => {
      startLoading();
      try {
        const data = await getPersistentVolumeClaims();
        setPersistant(data.items ?? []);
      } catch (err) {
        setError('Failed to fetch Persistant Volume Claims');
      } finally {
        stopLoading();
        setLoading(false);
      }
    };

    fetchPersistant();
  }, []);

  return { persistant, loading, error };
};