import { useEffect, useState } from 'react';
import { getStorageClasses } from '../../services/api/api';
import { useLoading } from '../../contexts/LoadingContext';


interface storageClass {
    metadata: {
      name: string;
      creationTimestamp: string;
    };
    provisioner: string;
    reclaimPolicy?: string;
    volumeBindingMode?: string;
    allowVolumeExpansion?: boolean;
    mountOptions?: string[];
    parameters?: Record<string, string>;
    annotations?: Record<string, string>;
  }
  

export const useStorageClasses = () => {
  const { startLoading, stopLoading } = useLoading();
  const [storage, setStorage] = useState<storageClass[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPersistant = async () => {
      startLoading();
      try {
        const data = await getStorageClasses();
        setStorage(data.items ?? []);
      } catch (err) {
        setError('Failed to fetch storage classes');
      } finally {
        stopLoading();
        setLoading(false);
      }
    };

    fetchPersistant();
  }, []);

  return { storage, loading, error };
};