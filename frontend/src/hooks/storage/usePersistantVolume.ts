import { useEffect, useState } from 'react';
import { getPersistentVolumes } from '../../services/api/api';
import { useLoading } from '../../contexts/LoadingContext';


interface persistantVolume {
  metadata: {
    name: string;
    creationTimestamp: string;
    namespace: string;
  };
  spec: {
    resources: {
      requests: {
        storage: string;
      };
    };
    capacity: {
      storage: string;
    }
    storageClassName?: string;
  };
  status: {
    phase: string; // Bound, Pending, etc.
  };
}

  

export const usePersistantVolume = () => {
  const { startLoading, stopLoading } = useLoading();
  const [persistant, setPersistant] = useState<persistantVolume[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPersistant = async () => {
      startLoading();
      try {
        const data = await getPersistentVolumes();
        setPersistant(data.items ?? []);
      } catch (err) {
        setError('Failed to fetch persistant volumes');
      } finally {
        stopLoading();
        setLoading(false);
      }
    };

    fetchPersistant();
  }, []);

  return { persistant, loading, error };
};