import { useEffect, useState } from 'react';

const useSelectedCluster = (): [string | null, (cluster: string) => void] => {
  const [selectedCluster, setSelectedCluster] = useState<string | null>(null);

  useEffect(() => {
    const storedCluster = localStorage.getItem('selectedCluster');
    if (storedCluster) {
      setSelectedCluster(storedCluster);
    }
  }, []);

  const updateSelectedCluster = (cluster: string) => {
    setSelectedCluster(cluster);
    localStorage.setItem('selectedCluster', cluster);
  };

  return [selectedCluster, updateSelectedCluster];
};

export default useSelectedCluster;
