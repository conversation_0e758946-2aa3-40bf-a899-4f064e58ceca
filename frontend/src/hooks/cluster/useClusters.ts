import { useEffect, useState, useCallback } from 'react';
import { getClusters } from '../../services/api/api';
import { useLoading } from '../../contexts/LoadingContext';

export interface ClusterInfo {
  name: string;
  upload_time: string;
}

export const useClusters = () => {
  const { startLoading, stopLoading } = useLoading();
  const [clusters, setClusters] = useState<ClusterInfo[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Use a ref to store the loading functions to avoid dependency issues
  const loadingFunctions = useCallback(() => {
    return { start: startLoading, stop: stopLoading };
  }, [startLoading, stopLoading]);

  const fetchClusters = useCallback(async () => {
    const { start, stop } = loadingFunctions();
    start();
    setLoading(true);
    setError(null);
    try {
      console.log('useClusters: Fetching clusters...');
      const data = await getClusters();
      console.log('useClusters: Clusters data received:', data);
      setClusters(data ?? []);
    } catch (err: any) {
      console.error('useClusters: Error fetching clusters:', err);
      // Provide more detailed error information
      const errorMessage = err.response?.data?.error || err.message || 'Failed to fetch clusters';
      setError(errorMessage);

      // Check if it's an authentication error
      if (err.response?.status === 401) {
        console.error('useClusters: Authentication error - redirecting to login');
        // Clear any existing token as it's invalid
        localStorage.removeItem('auth_token');
        // Redirect to login page
        window.location.href = '/login';
      }
    } finally {
      setLoading(false);
      stop();
    }
  }, [loadingFunctions]);

  useEffect(() => {
    fetchClusters();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Only run on mount

  return { clusters, loading, error, refetch: fetchClusters };
};
