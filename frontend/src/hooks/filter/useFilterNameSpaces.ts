import { useEffect, useState } from 'react';
import { getNamespaces } from '../../services/api/api';
import { useLoading } from '../../contexts/LoadingContext'; // Import the context

interface NameSpace {
  metadata: {
    name: string;
    creationTimestamp: string;
    labels?: { [key: string]: string };
  };

}

export const useFilterNameSpace = () => {
  const { startLoading, stopLoading } = useLoading(); // Access loading context
  const [namespace, setNameSpace] = useState<NameSpace[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchNameSpace = async () => {
      startLoading(); // Start the global loading bar
      try {
        const data = await getNamespaces();
        setNameSpace(data.items ?? []);
      } catch (err) {
        setError('Failed to fetch namespaces');
      } finally {
        stopLoading(); // Stop the global loading bar
        setLoading(false);
      }
    };

    fetchNameSpace();
  }, []); // Ensure that context functions are included in dependencies

  return { namespace, loading, error };
};
