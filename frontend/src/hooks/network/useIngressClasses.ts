import { useEffect, useState } from 'react';
import { getIngresseClass} from '../../services/api/api';
import { useLoading } from '../../contexts/LoadingContext';


interface ingresseClass {
  metadata: {
    name: string;
    creationTimestamp: string;
    namespace: string;
  };
  spec?: {
    controller?: string;
  };
  apiVersion?: string;
  scope?: string; // If available
  kind?: string;
}


export const useIngresseClasses = () => {
  const { startLoading, stopLoading } = useLoading();
  const [ingresseClass, setIngresseClass] = useState<ingresseClass[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchIngresseClass = async () => {
      startLoading();
      try {
        const data = await getIngresseClass();
        setIngresseClass(data.items ?? []);
      } catch (err) {
        setError('Failed to fetch IngresseClass');
      } finally {
        stopLoading();
        setLoading(false);
      }
    };

    fetchIngresseClass();
  }, []);

  return { ingresseClass, loading, error };
};