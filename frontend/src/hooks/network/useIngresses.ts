import { useEffect, useState } from 'react';
import { getIngresses} from '../../services/api/api';
import { useLoading } from '../../contexts/LoadingContext';


interface Ingresses {
    metadata: {
      name: string;
      creationTimestamp: string;
      namespace: string;
    };
    status?: {
      loadBalancer?: {
        ingress: { ip: string; hostname: string }[];
      };
    };
    spec?: {
      rules: {
        host: string;
        http: {
          paths: { path: string; backend: { serviceName: string } }[];
        };
      }[];
    };
  }

export const useIngresses = () => {
  const { startLoading, stopLoading } = useLoading();
  const [ingresses, setIngresses] = useState<Ingresses[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchIngresses = async () => {
      startLoading();
      try {
        const data = await getIngresses();
        setIngresses(data.items ?? []);
      } catch (err) {
        setError('Failed to fetch Ingresses');
      } finally {
        stopLoading();
        setLoading(false);
      }
    };

    fetchIngresses();
  }, []);

  return { ingresses, loading, error };
};