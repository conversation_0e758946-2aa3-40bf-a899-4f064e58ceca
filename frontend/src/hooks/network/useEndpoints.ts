import { useEffect, useState } from 'react';
import { getEndpoints} from '../../services/api/api';
import { useLoading } from '../../contexts/LoadingContext';


interface Endpoints {
    metadata: {
      name: string;
      creationTimestamp: string;
      namespace: string;
    };
    subsets?: {
      addresses?: {
        ip: string;
      }[];
      ports?: {
        port: number;
      }[];
    }[];
  }
  
  

export const useEndpoints = () => {
  const { startLoading, stopLoading } = useLoading();
  const [endpoints, setEndpoints] = useState<Endpoints[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchEndpoints = async () => {
      startLoading();
      try {
        const data = await getEndpoints();
        setEndpoints(data.items ?? []);
      } catch (err) {
        setError('Failed to fetch Endpoints');
      } finally {
        stopLoading();
        setLoading(false);
      }
    };

    fetchEndpoints();
  }, []);

  return { endpoints, loading, error };
};