import { useEffect, useState } from 'react';
import { getServices} from '../../services/api/api';
import { useLoading } from '../../contexts/LoadingContext';


interface Services {
    metadata: {
      name: string;
      creationTimestamp: string;
      namespace: string;
    };
    spec: {
      type: string;
      clusterIP: string;
      externalIPs?: string[];
      ports: {
        port: number;
        protocol?: string;
        targetPort?: number | string;
        nodePort?: number;
      }[];
      selector?: Record<string, string>;
    };
    status?: {
      loadBalancer?: {
        ingress?: { ip?: string; hostname?: string }[];
      };
    };
  }
  

export const useServices = () => {
  const { startLoading, stopLoading } = useLoading();
  const [services, setServices] = useState<Services[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchServices = async () => {
      startLoading();
      try {
        const data = await getServices();
        setServices(data.items ?? []);
      } catch (err) {
        setError('Failed to fetch Sevices');
      } finally {
        stopLoading();
        setLoading(false);
      }
    };

    fetchServices();
  }, []);

  return { services, loading, error };
};