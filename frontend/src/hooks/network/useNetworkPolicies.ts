import { useEffect, useState } from 'react';
import { getNetworkPolicies} from '../../services/api/api';
import { useLoading } from '../../contexts/LoadingContext';


interface NetworkPolicies {
    metadata: {
      name: string;
      creationTimestamp: string;
      namespace: string;
    };
    spec: {
      policyTypes: string[];
  }
}
export const useNetworkPolicies = () => {
  const { startLoading, stopLoading } = useLoading();
  const [networkPolicies, setNetworkPolicies] = useState<NetworkPolicies[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchNetworkPolicies = async () => {
      startLoading();
      try {
        const data = await getNetworkPolicies();
        setNetworkPolicies(data.items ?? []);
      } catch (err) {
        setError('Failed to fetch Network Policies');
      } finally {
        stopLoading();
        setLoading(false);
      }
    };

    fetchNetworkPolicies();
  }, []);

  return { networkPolicies, loading, error };
};