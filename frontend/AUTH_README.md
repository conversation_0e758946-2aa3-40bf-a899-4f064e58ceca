# Frontend Authentication System

This document explains how the authentication system works in the frontend application.

## Overview

The frontend authentication system uses Laravel Sanctum for token-based authentication. It communicates with the backend API to authenticate users and protect routes.

## How It Works

1. **Authentication Context**: The `AuthContext` provides authentication state and methods throughout the application.
2. **Token Storage**: Authentication tokens are stored in localStorage for persistence between sessions.
3. **API Integration**: All API requests automatically include the authentication token.
4. **Protected Routes**: Routes are protected based on the authentication state.

## Components

### AuthContext

The `AuthContext` provides:

- `isAuthenticated`: <PERSON><PERSON>an indicating if the user is authenticated
- `user`: The current user object
- `loading`: <PERSON><PERSON><PERSON> indicating if authentication is being checked
- `error`: Any authentication errors
- `login(email, password)`: Function to log in
- `logout()`: Function to log out

### Login Page

The login page:

- Collects user credentials
- Calls the login function from AuthContext
- Handles loading and error states
- Redirects to the dashboard on successful login

### Protected Routes

Routes are protected by checking the `isAuthenticated` state from AuthContext.

## Usage

### Logging In

```tsx
import { useAuth } from '../contexts/AuthContext';

const LoginPage = () => {
  const { login } = useAuth();
  
  const handleLogin = async () => {
    try {
      await login('<EMAIL>', 'password');
      // Redirect or perform actions after successful login
    } catch (error) {
      // Handle login error
    }
  };
  
  // ...
};
```

### Accessing User Data

```tsx
import { useAuth } from '../contexts/AuthContext';

const ProfilePage = () => {
  const { user } = useAuth();
  
  return (
    <div>
      <h1>Welcome, {user?.name}</h1>
      {/* Display user information */}
    </div>
  );
};
```

### Logging Out

```tsx
import { useAuth } from '../contexts/AuthContext';

const NavBar = () => {
  const { logout } = useAuth();
  
  const handleLogout = async () => {
    try {
      await logout();
      // Redirect to login page or perform other actions
    } catch (error) {
      // Handle logout error
    }
  };
  
  // ...
};
```

## API Requests

All API requests automatically include the authentication token. The token is added to the Axios instance in the `api.tsx` file.

Example API request:

```tsx
import axios from 'axios';

// The token is automatically included in the request headers
const fetchData = async () => {
  try {
    const response = await axios.get('/api/data');
    return response.data;
  } catch (error) {
    throw error;
  }
};
```

## Troubleshooting

If you encounter authentication issues:

1. Check that the backend API is running and accessible
2. Verify that the `VITE_API_BASE_URL` is correctly set in the `.env` file
3. Clear localStorage and try logging in again
4. Check browser console for any errors
5. Verify that the CORS settings on the backend allow requests from the frontend

## Security Considerations

- Tokens are stored in localStorage, which is vulnerable to XSS attacks
- The application uses HTTPS to protect data in transit
- CSRF protection is enabled for cookie-based authentication
- The backend validates all requests and tokens
