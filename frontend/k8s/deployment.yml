apiVersion: apps/v1
kind: Deployment
metadata:
  name: redalerts-front
  labels: 
    app: redalerts-front
  namespace: redalerts
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redalerts-front
  template:
    metadata:
      labels:
        app: redalerts-front
    spec:

      containers:
      - name: redalerts-front
        image: nexus.satoripop.io:8083/repository/projets/redalerts-front:latest


        imagePullPolicy: Always
        ports:
        - containerPort: 80
        env:
        - name: APP_LOCALE
          value: "en"
        - name: APP_FALLBACK_LOCALE
          value: "en"
        - name: APP_FAKER_LOCALE
          value: "en_US"
        envFrom:
        - configMapRef:
            name: redalerts-front
      imagePullSecrets:
        - name: nexus.satoripop.io