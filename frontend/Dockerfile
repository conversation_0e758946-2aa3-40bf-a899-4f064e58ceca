FROM nexus.satoripop.io:8083/repository/php-images/php-8.3-nginx-node20:latest
LABEL maintainer="<EMAIL>"


USER www-data
COPY --chown=www-data . .
ENV COMPOSER_MEMORY_LIMIT=-1
RUN cp k8s/.env-k8s .env
RUN npm install && npm run build
USER root
RUN rm -f /etc/nginx/nginx.conf
RUN cp k8s/nginx.conf /etc/nginx/nginx.conf

#START TO CHANGE BY DEVELOPPER


# (Optional) Copy built files to NGINX web root if needed
# RUN cp -r dist/* /var/www/html/

# Use root to start nginx (or configure permissions accordingly)
EXPOSE 80

