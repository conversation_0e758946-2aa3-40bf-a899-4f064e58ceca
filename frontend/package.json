{"name": "red-alert", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.0.2", "@mui/material": "^7.0.2", "@mui/x-data-grid": "^7.28.3", "@tailwindcss/vite": "^4.1.3", "@tanstack/react-table": "^8.21.2", "axios": "^1.8.4", "chart.js": "^4.4.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cron-parser": "^5.1.1", "cron-schedule": "^5.0.4", "date-fns": "^4.1.0", "lucide-react": "^0.487.0", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "react-router-dom": "^7.5.0", "react-toastify": "^11.0.5", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.3", "tw-animate-css": "^1.2.5"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.0.4", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react-swc": "^3.8.0", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}