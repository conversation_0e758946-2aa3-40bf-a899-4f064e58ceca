user  nginx;
worker_processes  4;

error_log  /var/log/nginx/error.log warn;
pid        /var/run/nginx.pid;

events {
    worker_connections  10240;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    sendfile        on;
    #tcp_nopush     on;
    client_max_body_size 200M;

    keepalive_timeout  65;

    log_format apm  '"$time_local" client=$remote_addr '
                    '"$request" request_length=$request_length status=$status '
                    'bytes_sent=$bytes_sent body_bytes_sent=$body_bytes_sent '
                    'referer="$http_referer" user_agent="$http_user_agent" '
                    'request_time=$request_time '
                    'upstream_addr=$upstream_addr upstream_status=$upstream_status '
                    'upstream_response_time=$upstream_response_time '
                    'upstream_connect_time=$upstream_connect_time '
                    'upstream_header_time=$upstream_header_time';

    server {
        listen 80;
        server_name _;
        root /var/www/html/public/;

        access_log /dev/stdout apm;
        error_log /dev/stderr;

        gzip                on;
        gzip_disable        "MSIE [1-6]\.(?!.*SV1)";
        gzip_proxied        any;
        gzip_buffers        16 8k;
        gzip_types          *;
        gzip_vary           on;

        index index.html index.htm index.php;

        charset utf-8;

        location / {
            try_files $uri $uri/ /index.php?$query_string;
            proxy_set_header X-Forwarded-Proto $scheme;

        }

        location ~* \.(js|jpg|jpeg|gif|png|css|tgz|gz|rar|bz2|doc|pdf|ppt|tar|wav|bmp|rtf|swf|ico|flv|txt|woff|woff2|svg|webp)$ {
    	try_files $uri $uri/ /index.php?$query_string;
    	expires 365d;
    	add_header Pragma "public";
    	add_header Cache-Control "public";
        add_header 'Content-Security-Policy' 'upgrade-insecure-requests';
        }

        location = /favicon.ico { access_log off; log_not_found off; }
        location = /robots.txt  { access_log off; log_not_found off; }

        error_page 404 /index.php;

        location ~ \index.php$ {
            fastcgi_pass 127.0.0.1:9000;
            fastcgi_index index.php;
            fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
            include fastcgi_params;
            fastcgi_read_timeout 3600;
        }

        location ~ /\.(?!well-known).* {
            deny all;
        }

        # Deny access to . files, for security
        location ~ /\. {
            log_not_found off;
            deny all;
        }

        # Allow fpm ping and status from localhost
        location ~ ^/(fpm-status|fpm-ping)$ {
            access_log off;
            allow 127.0.0.1;
            deny all;
            fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
            include fastcgi_params;
            fastcgi_pass 127.0.0.1:9000;
        }
    }
}