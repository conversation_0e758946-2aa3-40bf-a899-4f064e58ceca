APP_NAME=Redalerts
APP_ENV=production
APP_KEY=base64:sHYVuSiDJMrMq86/IrdhdnoZR2v5mtKOMAxVRPW23pA=
APP_DEBUG=true
APP_TIMEZONE=UTC
APP_URL=https://redalerts-back.k8s.satoripop.io
ASSET_URL=https://redalerts-back.k8s.satoripop.io
FRONTEND_URL=https://redalerts-front.k8s.satoripop.io
FORCE_HTTPS=true
APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=pgsql
DB_HOST=************
DB_PORT=5432
DB_DATABASE=redalerts_db
DB_USERNAME=redalerts_usr
DB_PASSWORD=E\uC6Na1H?3l

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database
CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"


KUBECONFIG_PATH=/var/www/html/storage/app/kubeconfigs

# n8n Integration
N8N_URL=https://n8n.redalerts.tn
N8N_WEBHOOK_UPLOAD_FILE=/webhook/upload-file
N8N_WEBHOOK_UPLOAD_KUBECONFIG=/webhook/upload-kubeconfig
