apiVersion: v1
kind: Service
metadata:
 name: redalerts-int
 namespace: redalerts
spec:
 selector:
   app: redalerts-int
 ports:
 - name: http
   protocol: TCP
   port: 80
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: redalerts-int
  namespace: redalerts
  annotations:
    acme.cert-manager.io/http01-edit-in-place: 'true'
    cert-manager.io/cluster-issuer: letsencrypt-dns01-issuer
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/ssl-redirect: 'true'
    nginx.ingress.kubernetes.io/force-ssl-redirect: 'true'
    nginx.ingress.kubernetes.io/proxy-read-timeout: '3600'
    nginx.ingress.kubernetes.io/proxy-send-timeout: '3600'

    nginx.ingress.kubernetes.io/server-snippets: |
      location / {
        add_header X-Robots-Tag "noindex, nofollow"
        proxy_set_header Upgrade $http_upgrade;
        proxy_http_version 1.1;
        proxy_set_header X-Forwarded-Host $http_host;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-For $remote_addr;
        proxy_set_header Host $host;
        proxy_set_header Connection "upgrade";
        proxy_cache_bypass $http_upgrade;

        } 
spec:
  tls:
    - hosts:
        - redalerts-back.k8s.satoripop.io
      secretName: redalerts-back-secret-tls
  rules:
    - host: redalerts-back.k8s.satoripop.io
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: redalerts-int
                port:
                  number: 80