apiVersion: batch/v1
kind: CronJob
metadata:
  name: redalerts-int-cron
  namespace: redalerts
spec:
  schedule: '* * * * *'
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            app: redalerts-int-cron
            k8s-app: redalerts-int
        spec:
          volumes:
            - name: pv-storage
              persistentVolumeClaim:
                claimName: redalerts-int-pvc
          containers:
            - name: redalerts-int-cron
              image: nexus.satoripop.io:8083/repository/projets/redalerts-int:latest
              command:
                - /bin/sh
              args:
                - -c
                - >
                  su -s /bin/bash www-data -c 'php /var/www/html/artisan schedule:run >> /dev/null 2>&1'
              volumeMounts:
                - name: pv-storage
                  mountPath: "/var/www/html/storage"
              envFrom:
                - configMapRef:
                    name: redalerts-int

              securityContext:
                capabilities:
                  add:
                    - NET_ADMIN
                    - SYS_ADMIN
                    - SYS_PTRACE
                privileged: true
              imagePullPolicy: Always
          restartPolicy: Never
          imagePullSecrets:
            - name: nexus.satoripop.io
          schedulerName: default-scheduler
  successfulJobsHistoryLimit: 1
  failedJobsHistoryLimit: 1