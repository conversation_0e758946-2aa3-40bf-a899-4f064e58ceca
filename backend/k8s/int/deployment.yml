apiVersion: apps/v1
kind: Deployment
metadata:
  name: redalerts-int
  labels: 
    app: redalerts-int
  namespace: redalerts
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redalerts-int
  template:
    metadata:
      labels:
        app: redalerts-int
    spec:
      volumes:
      - name: pv-storage
        persistentVolumeClaim:
          claimName: redalerts-int-pvc
      containers:
      - name: redalerts-int
        image: nexus.satoripop.io:8083/repository/projets/redalerts-int:latest
        command: ["/bin/sh"]
        args:
        - -ce
        - |
          /bin/bash <<'EOF'
          false | cp -avir /tmpstorage/* /var/www/html/storage/
          chown -R www-data:www-data /var/www/html/storage
          php artisan op:cl
          supervisord
          tail -f /dev/null
          EOF
        volumeMounts:
        - mountPath: "/var/www/html/storage"
          name: pv-storage
        imagePullPolicy: Always
        ports:
        - containerPort: 80
        env:
        - name: APP_LOCALE
          value: "en"
        - name: APP_FALLBACK_LOCALE
          value: "en"
        - name: APP_FAKER_LOCALE
          value: "en_US"
        envFrom:
        - configMapRef:
            name: redalerts-int
      imagePullSecrets:
        - name: nexus.satoripop.io