apiVersion: apps/v1
kind: Deployment
metadata:
  name: redalerts-int-queue
  labels:
    app: redalerts-int-queue
  namespace: redalerts
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redalerts-int-queue
  template:
    metadata:
      labels:
        app: redalerts-int-queue
    spec:
      volumes:
        - name: pv-storage
          persistentVolumeClaim:
            claimName: redalerts-int-pvc
      containers:
        - name: redalerts-int-queue
          image: nexus.satoripop.io:8083/repository/projets/redalerts-int:latest
          command: ["/bin/sh"]
          args:
            - -c
            - >
              su -s /bin/bash www-data -c 'php /var/www/html/artisan queue:work'
          volumeMounts:
            - name: pv-storage
              mountPath: "/var/www/html/storage"
          imagePullPolicy: Always
          envFrom:
            - configMapRef:
                name: redalerts-int
          resources:
            requests:
              memory: "512Mi"
              cpu: "1000m"
            limits:
              memory: "2048Mi"
              cpu: "2000m"
          securityContext:
            capabilities:
              add:
                - NET_ADMIN
                - SYS_ADMIN
                - SYS_PTRACE
            privileged: true
      imagePullSecrets:
        - name: nexus.satoripop.io