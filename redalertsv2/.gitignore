/.phpunit.cache
/node_modules
/public/build
/public/hot
/public/storage
/storage/*.key
/storage/pail
/vendor
.env
.env.backup
.env.production
.phpactor.json
.phpunit.result.cache
Homestead.json
Homestead.yaml
npm-debug.log
yarn-error.log
/auth.json
/.fleet
/.idea
/.nova
/.vscode
/.zed

# Keep kubectl-ai binary in version control
# but allow for updates
!/storage/kubectl-ai-google/
!/storage/kubectl-ai-google/kubectl-ai
!/storage/kubectl-ai-google/README.md
