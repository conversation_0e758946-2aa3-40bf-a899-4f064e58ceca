# kubectl-ai Google Binary

This folder contains the kubectl-ai binary from Google Cloud Platform for the redalertsv2 project.

## Contents

- `kubectl-ai` - The main binary executable (version 0.0.14)

## Source

Downloaded from: https://github.com/GoogleCloudPlatform/kubectl-ai
Release: v0.0.14
Date: 2025-06-17

## Usage

This binary is used by the redalertsv2 application to provide AI-powered Kubernetes assistance through the chatbot widget.

## Installation Details

- Original location: `/usr/local/bin/kubectl-ai`
- Project location: `storage/kubectl-ai-google/kubectl-ai`
- Permissions: Executable (755)

## Version Info

```
version: 0.0.14
commit: bc554dfd71e2829cd8986223a35ec738b2686367
date: 2025-06-17T01:33:09Z
```

## License

This binary is subject to the Apache-2.0 license from the original kubectl-ai project.
